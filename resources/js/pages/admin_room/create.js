$(document).ready(function() {

    var idSelected = [];
    var date = $("input[name='date_time'").val();
    $("select.time").attr('disabled', 'disabled');
    let tables = $('table').get();
    axios.get(laroute.route('admin.cast.list.type'), {
        params: {
            date: date,
            type: NO_SHARE_ROOM
        }
    }).then(function(response) {
        for (let index = 1; index <= tables.length; index++) {
            let table = $(`table[data-room-id="${index}"`);
            addOptionNameCast(response.data.data, table.find('select.cast_name'));
        }

    });


    $('.cast_name').on('focus', function() {
        castIdOld = $(this).find(":selected").val();
        castNameOld = $(this).find(":selected").html();
    }).on('change', function(e) {
        $(this).blur();
        let tableParent = $(this).closest('table');
        let roomId = tableParent.attr('data-room-id');
        let shift = $(this).attr('data-slot');
        tableParent.find(`.hour[data-slot="${shift}"]`).find('select').removeAttr('disabled');
        let castIdNew = $(this).find(":selected").val();
        let castNameNew = $(this).find(":selected").html();
        if (castIdNew == 0) {
            idSelected = idSelected.filter(item => item !== castIdOld);
            tableParent.find(`.hour[data-slot="${shift}"]`).find('select').val("-1");
            tableParent.find(`.hour[data-slot="${shift}"]`).find('select').attr('disabled', 'disabled');
            $(`.cast_name option[value="${castIdOld}"]`).remove();
            $('.cast_name').append(
                `<option value="${castIdOld}">${castNameOld} </option>`
            );
        } else {
            idSelected.push(castIdNew);
            let timeStart = getTimer(tableParent, 'start', roomId, shift);
            let timeEnd = getTimer(tableParent, 'end', roomId, shift);
            let date = $("input[name='date_time'").val();
            if (timeStart.indexOf('-1') < 0 && timeEnd.indexOf('-1') < 0) {
                $.ajax({
                    url: laroute.route('admin.check.time.cast'),
                    type: "GET",
                    data: {
                        date: date,
                        time_start: timeStart,
                        time_end: timeEnd,
                        cast_id: castIdNew
                    }
                }).done(function(message) {
                    let selectTime = tableParent.find(`.hour[data-slot="${shift}"]`);
                    setError(selectTime, message);
                });
            }
            $(".cast_name option[value='" + castIdNew + "']").remove();
            $(this).append(
                `<option value="${castIdNew}" selected>${castNameNew} </option>`
            );
            if (castIdOld != 0) {
                $(".cast_name option[value='" + castIdOld + "']").remove();
                $('.cast_name').append(
                    `<option value="${castIdOld}">${castNameOld} </option>`
                );
            }
        }
    });

    function getTimer(el, name, roomId, slot) {

        return el.find(`select[name="hour_${name}[${roomId}][${slot}]"]`).val() + ':' +
            el.find(`select[name="minute_${name}[${roomId}][${slot}]"]`).val();
    }

    function getAllTimerByRoomId(roomId) {
        let timers = [];
        let room = $(`table[data-room-id="${roomId}"]`);
        [1, 2, 3].forEach((slot) => {

            let parent = room.find(`.hour[data-slot="${slot}"]`);;
            let timeStart = getTimer(parent, 'start', roomId, slot)
            let timeEnd = getTimer(parent, 'end', roomId, slot)

            timers.push({
                timeStart: timeStart,
                timeEnd: timeEnd,
            });

        })
        return timers;
    }

    function setError(el, message) {
        let parentTd = el.closest('td');
        let error = parentTd.find('div.error');
        error.html(message);
    }

    function checkSetTime(shift) {
        if (shift.timeStart.indexOf('-1') < 0 && shift.timeEnd.indexOf('-1') < 0) {
            return 1;
        }
        return 0;
    }

    function checkTimeShift(shift1, shift2, el, message) {
        if (checkSetTime(shift1) && checkSetTime(shift2)) {
            if (shift1.timeEnd > shift2.timeStart) {
                setError(el, message);
            }
        }
    }

    $("select.time").on('change', function(e) {
        var curent = $(this);
        let all = getAllTimerByRoomId($(this).closest('table').attr('data-room-id'));
        let tableParent = $(this).closest('table');
        let roomId = tableParent.attr('data-room-id');

        let shift = $(this).closest(".hour").attr('data-slot');

        let castId = tableParent.find(`select[name="cast_id[${roomId}][${shift}]"]`).val();

        let date = $("input[name='date_time'").val();

        if (checkSetTime(all[shift - 1])) {
            if (all[shift - 1].timeStart > all[shift - 1].timeEnd) {
                setError(curent, '※適切な時間を入力してください。');
            } else {
                setError(curent, "");
                let timeStart = all[shift - 1].timeStart;
                let timeEnd = all[shift - 1].timeEnd;
                axios.get(laroute.route('admin.check.time.cast'), {
                    params: {
                        date: date,
                        time_start: timeStart,
                        time_end: timeEnd,
                        cast_id: castId
                    }
                }).then(function(response) {
                    if (response.data.message == '※キャストの出勤時間外です。') {
                        setError(curent, '※キャストの出勤時間外です。');
                    } else {
                        setError(curent, "");
                        switch (shift) {
                            case '1':
                                checkTimeShift(all[0], all[1], curent, '※部屋の使用時間が被っています。');
                                checkTimeShift(all[0], all[2], curent, '※部屋の使用時間が被っています。');
                                break;
                            case '2':
                                checkTimeShift(all[0], all[1], curent, '※部屋の使用時間が被っています。');
                                checkTimeShift(all[1], all[2], curent, '※部屋の使用時間が被っています。');
                                break;
                            case '3':
                                checkTimeShift(all[1], all[2], curent, '※部屋の使用時間が被っています。');
                                checkTimeShift(all[0], all[2], curent, '※部屋の使用時間が被っています。');

                        }
                    }

                });
            }
        }
    });

    var tableRomShareCurent = $('.check-share:checked').closest('table');
    $(".check-share").on('change', function(e) {
        let selectedOld = tableRomShareCurent.find("select.cast_name");
        selectedOld.val('0');
        tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
        tableRomShareCurent = $(this).closest('table');
        tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
        let room = tableRomShareCurent.attr('data-room-id');
        let select = tableRomShareCurent.find("select.cast_name");
        let selectAdd = '';
        [0, 1, 2].forEach(e => {
            let selected = select.eq(e).find(":selected");
            if (selected.val() > 0) {
                let id = selected.val();
                let name = selected.html();
                selectAdd += `<option value="${id}">${name}</option>`;
                idSelected = idSelected.filter(item => item !== id);
            }
        });
        if ($(this).prop("checked")) {
            axios.get(laroute.route('admin.cast.list.type'), {
                params: {
                    date: date,
                    type: SHARE_ROOM
                }
            }).then(function(response) {
                addOptionNameCast(response.data.data, select);
                tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
                tableRomShareCurent.find('.error').html("");
            })
        }
        axios.get(laroute.route('admin.cast.list.type'), {
            params: {
                date: date,
                type: NO_SHARE_ROOM
            }
        }).then(function(response) {
            $('table').each(function() {
                if ($(this).attr('data-room-id') != room) {
                    $(this).find('select.cast_name').append(selectAdd);
                }
            });
            addOptionNameCast(response.data.data, selectedOld);
            tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
            tableRomShareCurent.find('.error').html("");
        })
    });

    function addOptionNameCast(data, el) {
        let html = `<option value="0"></option>`;
        data.forEach(cast => {
            if (!idSelected.includes('' + cast.cast_id)) {
                html +=
                    `<option value="${cast.cast_id}" hour_start="${cast.hour_start}"
                hour_end="${cast.hour_end}">
                 ${cast.name_cast}
                </option>`;
            }
        });
        el.html(html);
    }
    $('#showPopup').on('click', function(e) {
        let date = $(this).attr('date-time');
        window.open(laroute.route('admin.show.popup.cast', {
            date: date
        }));
    })

    $('.btn-save').click(function() {
        showPopupOk('', ' 登録完了しました。', 'OK', function() {
            $('#form-room-calendar').submit();
        })
    })
});
