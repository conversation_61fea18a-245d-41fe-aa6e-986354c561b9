function toASCII(chars) {
    var ascii = '';
    for (var i = 0, l = chars.length; i < l; i++) {
        var c = chars[i].charCodeAt(0);
        if (c >= 0xFF00 && c <= 0xFFEF) {
            c = 0xFF & (c + 0x20);
        }
        ascii += String.fromCharCode(c);
    }
    return ascii;
}

$(function() {
    $("input[data-type='currency']").on({
        blur: function() {
            if ($(this).val().length > 0) {
                var val = Number(toASCII($(this).val()));
                if (val == 0) {
                    $(this).val(val);
                } else if (!val || val < 0) {
                    $(this).val('');
                } else {
                    $(this).val(val);
                }
            }
        }
    });

    $("input[data-type='number']").on({
        blur: function() {
            if ($(this).val().length > 0) {
                $(this).val($(this).val().replace(/\D/g,''));
            }
        }
    });
})
