@import "variables";
#arrange-cast {
    .group-button-top {
        display: flex;
        flex-direction: column;
        .btn-group {
            margin: 10px 0px 10px 0px;
            .btn-1 {
                max-width: 170px;
            }
            .btn-2 {
                max-width: 200px;
            }
        }
    }
    p {
        margin-bottom: 6px;
        font-size: 16px;
        color: #000000;
        font-weight: inherit;
        margin-left: 15px;
        span {
            font-size: 22px;
            font-weight: 500;
            margin-left: 15px;
        }
    }
    .sort {
        margin: 0 0 0 -15px;
        text-align: center;
    }
    .filter {
        .btn {
            font-weight: 400;
            color: white;
            border-radius: 25px;
        }
        .filter-1 {
            background-color: $yellow1;
        }
        .filter-2 {
            background-color: $orange;
        }
        button:active,
        .active {
            background-color: $yellow;
        }
    }
    .search-filter {
        margin-top: 10px;
        .form-search {
            margin-top: 5px;
        }
    }
    button.up {
        outline: none;
        height: 0px;
        width: 0px;
        background: white;
        border: none;
    }
    button.down {
        outline: none;
        height: 0px;
        width: 0px;
        background: white;
        border: none;
    }
    .fas.fa-caret-up {
        font-size: 25px;
    }
    .fas.fa-caret-down {
        font-size: 25px;
    }
    .dot {
        background: black;
        width: 10px;
        height: 10px;
        border-radius: 5px;
        margin: 0 auto;
    }
    .cast {
        margin-bottom: 50px;
    }
    .table-list {
        .title-table {
            padding: 2em 0 1em 0;
        }
        table {
            text-align: center;
            th,
            td {
                color: $black;
                font-size: 14px;
                padding-top: 0.55rem;
                padding-bottom: 0.55rem;
                vertical-align: middle;
                .fa-pencil-square-o {
                    font-size: 16px;
                }
            }
            th {
                font-size: 16px;
                border: 1px solid #EAEAEA;
                box-sizing: border-box;
                box-shadow: 0px 4px 4px rgb(0 0 0 / 5%);
                border-radius: 6px 6px 0px 0px;
            }
        }
    }
}
