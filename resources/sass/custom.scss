body {
    background: white;
    top: 20px;
    border-top: 25px solid #340100;
    font-family: 'Roboto', sans-serif;
}

.nopadding {
    padding: 0;
}

.nomargin {
    margin: 0;
}

.sidenav-header {
    height: auto;
    background: white;
}

.navbar-top {
    padding: 6px;
}

.admin-page-name {
    font-style: normal;
    font-weight: bold;
    font-size: 24px;
    line-height: 28px;
    color: black;
    margin: 46px 0px 0;
}

#sidenav-main {
    border-right: 0;
    position: relative;
    .navbar-brand {
        img {
            max-height: fit-content;
        }
    }
    .navbar-nav {
        // background: #ecf4f4;
        .nav-item {
            &:nth-child(3) {
                border-bottom: 1px solid #B9B9B9;
            }
            &:nth-child(1),
            &:nth-child(2),
            &:nth-child(3) {
                .nav-link {
                    // background: #ecf4f4;
                    color: #592C00;
                    font-weight: bold;
                }
            }
        }
        .nav-link {
            margin: 0;
            padding-left: 25px;
            padding-right: 10px;
            &.active {
                background: #F7EBDF;
            }
            span {
                padding-left: 8px;
            }
            i {
                width: 32px;
                height: 32px;
                background-repeat: no-repeat;
                background-size: 60%;
                background-position: 50%;
                &.icon-general {
                    background-image: url(../images/icons/general.png);
                }
                &.icon-status {
                    background-image: url(../images/icons/setting.png);
                }
                &.icon-timetable {
                    background-image: url(../images/icons/schedule.png);
                }
                &.icon-booking {
                    background-image: url(../images/icons/booking.png);
                }
                &.icon-daskboard {
                    background-image: url(../images/icons/setting-2.png);
                }
                &.icon-list-user {
                    background-image: url(../images/icons/icon_list_user.png);
                }
                &.icon-cast {
                    background-image: url(../images/icons/user-check.png);
                }
                &.icon-attendance {
                    background-image: url(../images/icons/calendar-check.png);
                }
                &.icon-course {
                    background-image: url(../images/icons/package.png);
                }
                &.icon-coupon {
                    background-image: url(../images/icons/coupon.png);
                }
                &.icon-option {
                    background-image: url(../images/icons/setting-3.png);
                }
                &.icon-customer {
                    background-image: url(../images/icons/customer.png);
                }
                &.icon-revenue {
                    background-image: url(../images/icons/price.png);
                }
                &.icon-room {
                    background-image: url(../images/icons/room.png);
                }
                &.icon-setting {
                    background-image: url(../images/icons/setting-4.png);
                }
            }
        }
    }
}

.header-page-name {
    text-align: center;
    font-weight: bold;
    content: " ";
    &::before {
        width: 30px;
        height: 30px;
        background: black;
        float: left;
    }
}

.main-page {
    display: flex;
}

.main-content {
    width: 100%
}

.modal-backdrop {
    z-index: 9998;
}

.js-default-ok-popup {
    z-index: 100000;
}

.js-default-popup {
    z-index: 100000;
}

.price-red {
    color: #ff2d15;
}
.price-blue {
    color: #00a4d8;
}
