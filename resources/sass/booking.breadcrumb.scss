@import "variables";
.breadcrumb-menu {
    width: calc(100% - 220px);
}

.main-breadcrumb {
    position: fixed;
    bottom: 13px;
    width: calc(100% - 80px);
    background: white;
    padding-bottom: 10px;
}

.breadcrumb-buttons {
    position: fixed;
    right: 40px;
    bottom: 20px;
    width: 220px;
    display: flex;
    height: 50px;
    z-index: 100;
}

.booking-breadcrumb {
    .describe {
        font-size: 11px;
    }
    .btn-breadcrumb {
        overflow: hidden;
        .cast-btn {
            &:not(:last-child) {
                &:after {
                    content: " ";
                    display: block;
                    width: 0;
                    height: 0;
                    border-top: 20px solid transparent;
                    border-bottom: 20px solid transparent;
                    border-left: 20px solid $white;
                    position: absolute;
                    top: 54%;
                    margin-top: -21px;
                    left: 100%;
                    z-index: 9;
                }
                &:before {
                    content: " ";
                    display: block;
                    width: 0;
                    height: 0;
                    border-top: 20px solid transparent;
                    border-bottom: 20px solid transparent;
                    position: absolute;
                    top: 54%;
                    margin-top: -21px;
                    margin-left: 1px;
                    left: 100%;
                    z-index: 9;
                }
            }
        }
        .cast-btn.cast-btn-warning.active {
            &:not(:last-child) {
                &:after {
                    border-left: 20px solid $yellow1;
                }
                &:before {
                    border-left: 20px solid $white;
                }
            }
        }
        .cast-btn.cast-btn-warning {
            &:not(:last-child) {
                &:before {
                    border-left: 20px solid $white;
                }
                &:after {
                    border-left: 20px solid $wheat;
                }
            }
            &:hover,
            &:active,
            &:focus {
                &:not(:last-child) {
                    &:after {
                        border-left: 20px solid $yellow1;
                    }
                    &:before {
                        border-left: 20px solid $white;
                    }
                }
            }
        }
    }
    .cast-btn {
        display: inline-block;
        margin-bottom: 0;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        background-image: none;
        border: 1px solid transparent;
        white-space: nowrap;
        font-size: 11px;
        line-height: 16px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        -o-user-select: none;
        user-select: none;
        position: relative;
        padding-top: 11px;
        height: 36px;
        width: 155px;
        padding-left: 20px;
    }
    .cast-btn:first-child {
        width: 140px;
    }
    .cast-btn:last-child {
        padding-left: -20px;
    }
    .cast-btn-warning {
        color: #414141;
        background-color: $wheat;
        border-color: $wheat;
        &:hover {
            color: $white;
            background-color: $yellow1;
            border-color: $yellow1;
        }
        &:focus {
            color: $white;
            background-color: $yellow1;
            border-color: $yellow1;
        }
        &:active {
            color: $white;
            background-color: $yellow1;
            border-color: $yellow1;
        }
    }
    .active {
        color: $white;
        background-color: $yellow1;
        border-color: $yellow1;
    }
    .cast-btn-warning.active {
        color: $white;
        background-color: $yellow1;
        border-color: $yellow1;
    }
    a.cast-btn.disabled {
        pointer-events: none;
    }
    fieldset {
        &:disabled {
            a.cast-btn {
                pointer-events: none;
            }
        }
    }
}

.booking-btn-next {
    display: inline-block;
    button {
        width: 9em;
        height: 36px;
        padding: 0;
        border-radius: 100px;
        margin-right: 20px;
        background-color: $gray;
        border: 1px solid $gray;
        color: $white;
        &:hover,
        &:focus {
            color: $white;
        }
    }
    &.active {
        button {
            color: $white;
            background-color: $orangeYellowH;
            border-color: $orangeYellowH;
        }
    }
}

.highlight {
    background-color: wheat;
    color: white;
}

.mask {
    display: none;
    position: fixed;
    z-index: 10;
    background-color: gray;
    opacity: 0.6;
}

.btn-nav-bottom {
    display: inline-block;
    .dropup {
        position: relative;
        margin-right: -5px;
        button {
            border-radius: 50%;
            height: 40px;
            padding: 0;
            width: 40px;
            background-color: $orangeYellow1;
            position: relative;
            z-index: 12;
        }
        .booking-item {
            padding-left: 10px;
        }
        .dropdown-toggle {
            &::after {
                display: none;
            }
        }
    }
    .dropdown-menu {
        padding: 15px 25px 0px 15px;
        bottom: 50px !important;
        left: auto !important;
        right: 100px !important;
        width: 215px;
        transition: 2s;
        a {
            border: 2px solid $green1;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 15px;
            padding: 5px;
            width: 60%;
            flex: 1;
        }
        div.img-icon {
            width: 40px;
            height: 40px;
            margin-left: 10px;
            text-align: center;
            padding-top: 7px;
            border: 1px solid;
            border-radius: 20px;
            img {
                width: 25px;
                height: 25px;
            }
        }
    }
}

.position-bottom {
    position: absolute;
    bottom: 0;
    right: 0;
}

.page-vertical {
    .breadcrumb-menu {
        width: 100%;
    }
}

.admin-booking {
    .breadcrumb-menu {
        width: calc(100% - 220px);
    }
    .main-breadcrumb {
        bottom: 23px;
        width: calc(100% - 350px);
    }
    .breadcrumb-buttons {
        bottom: 30px;
    }
}

@media screen and (max-width: 1024px) {
    .admin-booking .main-breadcrumb {
        width: calc(100% - 300px);
    }
    .admin-booking .breadcrumb-buttons {
        right: 0px;
    }
}

@media (max-width: 1680px) {
    .btn-group,
    .btn-group-vertical {
        display: flex;
    }
}

.booking-breadcrumb {
    .cast-btn {
        width: 130px;
    }
    .cast-btn:first-child {
        width: 120px;
    }
}

@media (min-width: 1900px) {
    #page-booking-pay-confirm {
        .breadcrumb-menu {
            .btn-option-coupon {
                display: inline-block;
            }
        }
    }
}
