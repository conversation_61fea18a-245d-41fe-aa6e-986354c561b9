@import "variables";
.manage-schedule-cast {
    .group-button-top {
        display: flex;
        flex-direction: column;
        .btn-group {
            margin: 10px 0px 10px 0px;
            .btn-1 {
                max-width: 170px;
            }
            .btn-2 {
                max-width: 200px;
            }
        }
    }
    .btn-submit {
        color: #ffffff;
        border: 2px solid #2dce89;
        border-radius: 20px;
        text-align: center;
        width: 20%;
        background-color: #2dce89;
    }
    .btn-status {
        pointer-events: none;
        text-align: center;
        margin-top: 1%;
        .btn {
            border: 2px solid $pink;
            border-radius: 20px;
            width: 25%;
            padding: 12px;
            &.active {
                background-color: $pink;
                color: $white;
            }
        }
    }
    .alert {
        text-align: center;
        color: red;
        font-size: 18px;
        display: none;
        margin: 0 auto;
    }
    .form-picture {
        color: black;
        .image-upload {
            margin-top: 30px;
            span {
                margin-left: 10px;
            }
            p {
                font-weight: initial;
            }
            p.infor-cast {
                background-image: linear-gradient(to right, black 38%, rgba(255, 255, 255, 0) 0%);
                background-position: bottom;
                background-size: 4px 2px;
                background-repeat: repeat-x;
                border-top: none;
                border-left: none;
                border-right: none;
            }
            p.newbie-term {
                color: red;
                text-align: right;
            }
        }
    }
    .cast-item-1 {
        width: 100%;
        height: 400px;
        margin: 0;
        box-shadow: 0px 4px 12px -4px rgb(0 0 0 / 19%);
        border-radius: 10px;
        margin-top: 30px;
        .img-cast {
            height: 300px;
            width: 100%;
            object-fit: cover;
            border-radius: 10px 10px 0px 0px;
        }
        .image-cast {
            .disabled {
                opacity: 0.4;
            }
        }
        .information-cast {
            margin: 10px 5px;
            p {
                font-size: 12px;
                margin: 0;
                height: 30px;
            }
        }
        .body-measurements {
            display: flex;
            p {
                margin-right: 5px;
            }
        }
    }
    .date-picker-wrapper .drp_top-bar {
        display: none;
    }
    .date-picker-wrapper.no-shortcuts {
        border: none;
        background: white;
        width: 100% !important;
    }
    .date-picker-wrapper .month-wrapper {
        border: none;
        width: 100% !important;
    }
    .date-picker-wrapper .gap {
        display: none;
    }
    .date-range12-container {
        width: 100%;
    }
    .date-picker-wrapper .month-wrapper table {
        width: 48%;
        margin-right: 2%;
        font-size: 18px;
        color: black;
        border: 1px solid black;
        border-top: 1px solid black;
        border-bottom: 1px solid black;
        border: none;
    }
    .date-picker-wrapper .month-wrapper table.month2 {
        width: 48%;
        font-size: 18px;
        color: black;
        border: none;
    }
    th,
    td {
        border: 1px solid black;
        width: 10%;
        p {
            margin: 7px 0 0 0;
            font-size: 10px;
        }
        div.day {
            margin: 0 0 0 0 !important;
        }
    }
    .date-picker-wrapper .month-wrapper table .day.toMonth.valid {
        font-size: 24px;
        font-weight: 600;
        height: -webkit-fill-available;
        height: 70px;
    }
    .date-picker-wrapper .month-wrapper table .day {
        padding: 17px 0;
    }
    th {
        height: 50px;
        border: none;
    }
    td {
        border: none;
    }
}

.modal-dialog {
    color: black;
    .modal-content {
        width: 800px;
        margin: 0 0 0 -120px;
        .modal-title {
            color: black;
            font-size: 26px;
            font-weight: 400;
            margin-left: 20%;
            .title {
                font-size: 18px;
            }
        }
        .modal-body {
            width: 800px;
            text-align: unset !important;
            font-weight: inherit !important;
            .alert,
            .alert-1,
            .alert-2,
            .alert-3,
            .alert-4 {
                text-align: center;
                color: red;
                font-size: 18px;
                display: none;
            }
            .date-end {
                margin-left: 100px;
            }
            .date {
                display: flex;
                margin-left: 70px;
            }
            .time {
                display: flex;
                margin: 20px 0 0 40px;
            }
            .hour-start {
                width: 100px;
                margin: 0 18px 0 20px;
            }
            .minute-start {
                width: 100px;
            }
            .middle {
                margin: -15px 64px 0 62px;
                font-size: 30px;
            }
            .hour-end {
                width: 100px;
                margin: 0 18px 0 0;
            }
            .minute-end {
                width: 100px;
            }
            .selection {
                display: flex;
                margin: 50px 0 0 120px;
                .room {
                    display: flex;
                    label {
                        margin: 0 0 0 10px;
                    }
                }
                .off {
                    margin-left: 30px;
                    display: flex;
                    label {
                        margin-left: 10px;
                    }
                }
            }
            .delete {
                margin-top: 38px;
                display: flex;
                margin-left: 320px;
                display: none;
                label {
                    margin-left: 10px;
                }
            }
            input[type=checkbox] {
                height: 20px;
                width: 20px;
            }
            .btn-submit {
                margin: 20px 0 0 210px;
                width: 40%;
                color: $white;
                font-weight: unset;
                background-color: $btnSave;
                border: $btnSave;
                border-radius: 22px;
            }
        }
    }
}
