@import "variables";
.tablet-booking {
    background-color: #E5E5E5;
}

.confirm-page {
    background-color: #E5E5E5;
    color: black;
    padding-top: 10px;
    .main-breadcrumb {
        background: none;
    }
    .col-sm-6 {
        color: #414141;
        .text-head {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
        }
        .text-row,
        .text-row-2 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 7px 0px;
            .text {
                font-size: 14px;
            }
        }
        .text-row {
            border-bottom: 1px solid #D7D7D7;
        }
        .spikes,
        .spikes-2,
        .spikes-3 {
            position: relative;
            background: white;
            padding: 10px;
            border-radius: 5px;
        }
        .spikes-2,
        .spikes-3 {
            padding: 10px;
        }
        .spikes-3 {
            background-color: #F7EBDF;
            margin-top: 10px;
            .text-row {
                border-bottom: 1px solid#787878;
            }
            .text-row-3 {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 40px;
                margin-top: 0px;
                .text {
                    font-size: 14px;
                }
            }
            #use-point {
                border: 1px solid #636363;
                border-radius: 5px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 5px 25px;
                min-width: 120px;
                background-color: #FFE6A6;
            }
            input {
                width: 50%;
            }
        }
    }
    .head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        .label {
            display: flex;
            width: 25%;
            .member {
                border: 1px solid #636363;
                border-radius: 5px;
                width: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 5px 20px;
                margin-left: 10px;
            }
            input {
                display: none;
            }
            .selected {
                background-color: #D26700;
                color: white;
            }
        }
        .text p {
            font-size: 20px;
            font-weight: 700;
        }
    }
    .button-confirm {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .btn-confirm {
            padding: 5px 25px;
            background-color: #9EF233;
            border-radius: 5px;
            font-weight: 700;
            font-size: 16px;
            border: 1px solid #636363;
            margin: 10px 10px 20px 0px;
        }
    }
    .modal {
        .content-popup {
            padding: 2% 5%;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration-style: solid;
        }
        .modal-content {
            padding: 3% 7%;
        }
        .clearfix {
            font-size: 20px;
            .text-box,
            .text-box-2 {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                padding: 2% 7%;
                .point {
                    color: red;
                }
            }
            .text-box-2 {
                margin-top: 20px;
                justify-content: flex-end;
            }
            .input-point {
                font-size: 40px;
                width: 75%;
                margin: 2% 0 2% 10%;
            }
            .form-control {
                color: $black;
            }
        }
    }
    .layer-left {
        position: absolute;
        z-index: 100;
        width: 105%;
        top: -5%;
        left: -1%;
    }
    .background {
        position: absolute;
        width: 106%;
        height: 125%;
        top: -4%;
        left: -3%;
    }
    .border-none,
    .border-none-2 {
        border: none;
        width: 50%;
    }
    .border-none:disabled {
        background-color: #F7EBDF;
    }
    .border-none-2:disabled {
        background-color: white;
    }
    .alert-danger {
        padding: 0;
        color: red;
        background-color: white;
        border: none;
        margin: 0;
        font-size: 12px;
        margin-left: 10%;
    }
    .overlay {
        position: fixed;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        display: none;
        background-color: #000;
        opacity: 0.5;
    }
    .popup {
        display: none;
        position: fixed;
        left: 50%;
        top: 20%;
        width: 400px;
        height: 200px;
        transform: translate(-50%, -50%);
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        .content-popup {
            height: 90px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .clearfix {
            margin-top: 25px;
            display: flex;
            justify-content: flex-end;
            button {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                border: none;
                cursor: pointer;
                opacity: 0.9;
                text-align: right;
                border-radius: 5px;
                margin: 0px 5px 0px 5px;
                padding: 10px 15px 10px 15px;
            }
            button:hover {
                opacity: 1;
            }
            .cancelbtn,
            .deletebtn {
                float: left;
                height: 40px;
            }
            .cancelbtn {
                background-color: #ccc;
                color: black;
            }
            .deletebtn {
                background-color: #f44336;
            }
            .submitbtn {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                border: none;
                cursor: pointer;
                opacity: 0.9;
                text-align: right;
                border-radius: 5px;
                margin: 0px 5px 0px 5px;
                padding: 10px 15px 10px 15px;
                background-color: #0088ff;
            }
            .submitbtn:hover {
                opacity: 1;
            }
        }
        .container {
            padding: 16px;
            text-align: center;
        }
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
    }
    .bg-information {
        .background {
            height: 120%;
        }
    }
}

.admin-booking {
    @media screen and (max-width: 1200px) {
        .head {
            label, button {
                width: auto !important;
            }
        }
    }
}
