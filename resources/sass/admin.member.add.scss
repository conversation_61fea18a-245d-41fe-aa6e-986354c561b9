@import "variables";
#manage-create-member {
    .group-button-top {
        .btn-group {
            .btn {
                width: 135px;
            }
        }
    }
    form {
        width: -webkit-fill-available;
        margin: 0 auto;
        margin-top: 60px;
        width: 80%;
        color: $black;
        label,
        p {
            margin: -4px 0 0 0;
            color: $black;
        }
        .input-custom {
            margin: 4px 0 0 0;
        }
        .customer-code {
            margin: 7px 0 0 0;
        }
        label.text-delete {
            margin: 0 0 0 0;
        }
    }
    input.delete {
        position: relative;
        float: right;
        height: 42px;
        width: 22px;
    }
    .btn-manage2 {
        border: $btnSave;
        margin: 10px 0px 0px 90px;
        width: 20%;
        padding: 10px 0px 10px 0px;
        border-radius: 20px;
        background-color: $btnSave;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: $white;
    }
    .alert-error {
        color: $red;
    }
    select {
        margin-top: 6px;
    }
    select.year-birth {
        height: 26px;
        width: 80px;
        margin: 0 8px 0 0;
    }
    select.month-birth {
        height: 26px;
        width: 80px;
        margin: 0 8px 0 4px;
    }
    select.day-birth {
        height: 26px;
        width: 80px;
        margin: 0 8px 0 4px;
    }
}
