@inject('mOrder', 'App\Models\Order' )
@inject('mCast', 'App\Models\Cast' )
@inject('mCourse', 'App\Models\Course' )
@inject('orderService', 'App\Services\OrderService')
@php
use Carbon\Carbon;
use App\Constants\Orders;
$now = Carbon::now();
if ($now->hour < CLOSE_HOURS - 24) {
    $now->subDay();
}
@endphp

@extends('admin.layouts.app')

@section('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="{{ assetMix('/css/manage.status.css') }}" type="text/css">
@endsection

@section('title', '現状管理')

@section('adminPageName')
    <h2>現状管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page"> 現状管理 </li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="managestatus">
        <div class="btn-header">
            <a class="btn btn-manage btn-ct-primary active-primary" role="button">現状管理</a>
            <a href="" class="btn btn-edit disabled" role="button">現状管理編集</a>
        </div>
        <div class="showstatus">
            <p class="text">発進前</p>
            <p class="number no-happened">{{ $orderNotHappened }}</p>
            <p class="text">通常</p>
            <p class="number happenning">{{ $orderHappenning }}</p>
            <p class="text">延長</p>
            <p class="number extension">{{ $orderExtension }}</p>
            <p class="text">完了</p>
            <p class="number finshed">{{ $orderFinshed }}</p>
            <p class="text">合計</p>
            <p class="number total">{{ $orderNotHappened + $orderHappenning + $orderExtension + $orderFinshed }}</p>
        </div>
        <div class="date">
            <div id="date-select"></div>
            <div class="img-calendar">
                <form action="{{ route('admin.manage.status') }}" method="GET" id="search-calendar">
                    <div class="date-choose">
                        <img src="{{ asset('images/icons/calender_clock.png') }}" alt="calender clock"
                            class="datepicker img-datepicker">
                        <div class="text-date">{{ formatDateJp($dateSelect) }}</div>
                        <input type="date" name="date" class="hidden"
                            value="{{ $dateSelect->toDateString() }}">
                    </div>
                </form>
            </div>
        </div>
        <div class="showtable table-responsive">
            <table class="table table-hover table-shadown">
                <thead>
                    <tr>
                        <th>編集</th>
                        <th class="stt">お客様 <br> 番号</th>
                        <th>状態</th>
                        <th colspan="2">キャスト名</th>
                        <th>部屋番号</th>
                        <th colspan="2">コース</th>
                        <th>種別</th>
                        <th>
                            モニター <br> チェック
                        </th>
                        <th>入浴前 <br> アンケート</th>
                        <th>入浴後 <br> アンケート</th>
                        <th class="time">入り時間</th>
                        <th class="time">上がり時間</th>
                        <th colspan="2" class="name">お客様名</th>
                        <th colspan="2">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if (!$orderAll)
                        <p class="text-center">※該当なデータがありません</p>
                    @else
                        @php
                            $castIds = array_values(array_unique($orderAll->pluck('cast_id')->all()));
                        @endphp
                        @foreach ($orderAll as $order)
                            <tr
                                class="tr-order status-normal" data-order-status="{{$order->status}}"
                                data-order-is_ten_minutes_before="{{$order->is_ten_minutes_before}}"
                                data-order-can_edit="{{$dateSelect->format('Y-m-d') == $now->format('Y-m-d')}}">
                                <td rowspan="3">
                                    @php
                                        $yesterday = clone $now;
                                        $yesterday = $yesterday->subDay();
                                        $canEdit = $dateSelect->format('Y-m-d') == $now->format('Y-m-d') || $dateSelect->format('Y-m-d') == $yesterday->format('Y-m-d');
                                    @endphp
                                    <div>
                                        @if ($canEdit)
                                            <a href="{{ route('admin.manage.status.edit', $order->id) }}" role="button"
                                                class="edit"> <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                            </a>
                                        @else
                                            <div class="edit"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></div>
                                        @endif
                                    </div>
                                </td>
                                <td rowspan="3" class="text-white">{{ $order->number }}</td>
                                <td class="status text-white"></td>
                                <td colspan="2" class="text-white">{{ $order->casts->name_cast }}</td>
                                <td class="text-white">
                                    @if (isset($castRoomCalendars[$order->cast_id]))
                                        {{ $castRoomCalendars[$order->cast_id]['room_name'] }}
                                    @endif
                                </td>
                                <td colspan="2" class="text-white">
                                    @if (isset($mCourse::withTrashed()->find($order->course_id)->name))
                                        {{ $mCourse::withTrashed()->find($order->course_id)->name }}
                                    @else ※データ無し
                                    @endif
                                </td>
                                <td class="text-white">
                                    @if ($order->type_fee == $mOrder::TYPE_APPOINT)
                                        指名
                                    @elseif ($order->type_fee == $mOrder::TYPE_NET)
                                        ネット
                                    @elseif($order->type_fee == $mOrder::TYPE_PANEL) パネル
                                    @else フリー
                                    @endif
                                </td>
                                <td class="text-white">
                                    @if ($order->casts->monitor_check == $mCast::CHECK_MONITOR)
                                        有り
                                    @else 無し
                                    @endif
                                </td>
                                <td class="text-white">
                                    @if ($order->is_first == $mOrder::FIRST_TIME)
                                        要
                                    @else 不要
                                    @endif
                                </td>
                                <td class="text-white">
                                    @if ($order->is_first == $mOrder::FIRST_TIME)
                                        要
                                    @else 不要
                                    @endif
                                </td>
                                @php
                                    $timeCourse = $mCourse::withTrashed()->find($order->course_id)->time ?? 0;
                                @endphp
                                <td class="time-start text-white">
                                    <p class="time-start-plan text-white">
                                        @php
                                            $dateStartString = $order->actual_date_start ? $order->actual_date_start : $order->date_start;
                                            $dateStart = Carbon::parse($dateStartString);
                                            $hourStart = $dateStart->hour;
                                            if ($dateStart->format('d') != Carbon::parse($order->date)->format('d')) {
                                                $hourStart = $dateStart->hour + 24;
                                            }
                                        @endphp
                                        {{ sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $dateStart->minute) }}
                                    </p>
                                </td>
                                <td class="time-end">
                                    <p class="time-end-plan text-white">
                                        @php
                                            $timeStart = Carbon::parse($order->date_start);
                                            $timeEnd = Carbon::parse($order->date_end);
                                            // if ($order->actual_date_start) {
                                            //     if (!$order->actual_date_end) {
                                            //         $actualStart = Carbon::parse($order->actual_date_start);
                                            //         $timeEnd = $actualStart->addMinutes($timeEnd->diffInMinutes($timeStart));
                                            //     }
                                            //     $timeStart = Carbon::parse($order->actual_date_start);
                                            // }
                                            if ($order->actual_date_end) {
                                                $timeEnd = Carbon::parse($order->actual_date_end);
                                            }
                                            $hourEnd = $timeEnd->hour;
                                            if ($timeEnd->day != Carbon::parse($order->date)->day) {
                                                $hourEnd = $timeEnd->hour + 24;
                                            }
                                        @endphp
                                        {{ sprintf('%02d', $hourEnd) . ':' . sprintf('%02d', $timeEnd->minute) }}
                                    </p>
                                    <p class="time-end-actual text-white">
                                        @if ($order->minute_extension && $order->status != $mOrder::STATUS_FINISH)
                                            @php
                                                $dateEndActual = clone $timeEnd;
                                                $dateEndActual->addMinutes($order->minute_extension);
                                                $hourEndActual = $dateEndActual->hour;
                                                if ($dateEndActual->day != Carbon::parse($order->date)->day) {
                                                    $hourEndActual = $dateEndActual->hour + 24;
                                                }
                                            @endphp
                                            ({{ sprintf('%02d', $hourEndActual) . ':' . sprintf('%02d', $dateEndActual->minute) }})
                                        @endif
                                    </p>
                                </td>
                                <td colspan="2" class="text-white">
                                    {{ $order->name_customer }}
                                </td>
                                <td colspan="2">
                                    <div class="action" order-id={{ $order->id }}
                                        cast-id={{ $order->cast_id }} status={{ $order->status }}>
                                        <button class="btn btn-table btn-back-start"
                                            role="button" status-id="{{ $mOrder::STATUS_NO_HAPPEN_YET }}"><i
                                                class="fa fa-undo" aria-hidden="true"></i></button>
                                        <button
                                            class="btn btn-table btn-start" role="button"
                                            status-id="{{ $mOrder::STATUS_HAPPENING }}">発進</button>
                                        <button
                                            class="btn btn-table btn-back-finsh"
                                            role="button"
                                            status-id="{{ $order->minute_extension ? $mOrder::STATUS_EXTENSION : $mOrder::STATUS_HAPPENING }}"><i
                                                class="fa fa-undo" aria-hidden="true"></i></button>
                                        <button
                                            class="btn btn-table btn-finsh" role="button"
                                            status-id="{{ $mOrder::STATUS_FINISH }}">完了</button>
                                        <button
                                            class="btn btn-table btn-back-ten-minute-before"
                                            role="button" style="width: 75px;"
                                            status-id="{{ $order->minute_extension ? $mOrder::STATUS_EXTENSION : $mOrder::STATUS_HAPPENING }}"><i
                                                class="fa fa-undo" aria-hidden="true"></i>&nbsp;10 分前</button>
                                        <button
                                            class="btn btn-table btn-ten-minute-before" role="button"
                                            status-id="{{ $order->minute_extension ? $mOrder::STATUS_EXTENSION : $mOrder::STATUS_HAPPENING }}">10 分前</button>
                                        <button class="btn btn-table btn-extension" role="button"
                                            status-id="{{ $mOrder::STATUS_EXTENSION }}">延長</button>
                                        <button
                                            class="btn btn-table btn-back-extension"
                                            role="button"
                                            data-count={{ $order->minute_extension ? $order->minute_extension / $minuteExtension : 0 }}
                                            status-id="{{ $mOrder::STATUS_HAPPENING }}"><i class="fa fa-undo"
                                                aria-hidden="true"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr rowspan="1" colspan="12" class="mini-table">
                                <th class="font-weight-normal">予約時金額</th>
                                <th class="font-weight-normal">指名料金</th>
                                <th class="font-weight-normal">予約時合計金額</th>
                                <th class="font-weight-normal">割引名目 1</th>
                                <th class="font-weight-normal">割引名目 2</th>
                                <th class="font-weight-normal">割引名目 3</th>
                                <th class="font-weight-normal">割引額 1</th>
                                <th class="font-weight-normal">割引額 2</th>
                                <th class="font-weight-normal">割引額 3</th>
                                <th class="font-weight-normal">割引額合計</th>
                                <th class="font-weight-normal">スタートプラス</th>
                                <th class="font-weight-normal">オプション</th>
                                <th class="font-weight-normal">支払い方法</th>
                                <th class="font-weight-normal">ユーザー支払い金額</th>
                                <th class="font-weight-normal">キャストへの支払い額</th>
                                <th class="font-weight-normal">店売上</th>
                            </tr>
                            @php
                                $totalOrderPrice = calculateOrderTotalPrice($order);
                                $totalOrderPriceShow = calculateCustomerPayOrderTotalPrice($order);
                            @endphp
                            <tr rowspan="1" colspan="12" class="mini-table">
                                <th class="font-weight-normal">
                                    {{ number_format($order->course_price) }} 円
                                </th>
                                <th class="font-weight-normal">
                                    {{ number_format($order->price_fee_new) }} 円
                                </th>
                                <th class="font-weight-normal">
                                    {{ number_format($order->course_price + $order->price_fee_new) }} 円
                                </th>
                                <th class="font-weight-normal">
                                    {{ $order->coupon_1_name ? $order->coupon_1_name : '-' }}
                                </th>
                                <th class="font-weight-normal">
                                    {{ $order->coupon_2_name ? $order->coupon_2_name : '-' }}
                                </th>
                                <th class="font-weight-normal">
                                    {{ $order->coupon_3_name ? $order->coupon_3_name : '-' }}
                                </th>
                                <th class="font-weight-normal">
                                    {{ $order->coupon_1_price ? number_format($order->coupon_1_price) . '  円' : '-' }}
                                </th>
                                <th class="font-weight-normal">
                                    {{ $order->coupon_2_price ? number_format($order->coupon_2_price) . '  円' : '-' }}
                                </th>
                                <th class="font-weight-normal">
                                    {{ $order->coupon_3_price ? number_format($order->coupon_3_price) . '  円' : '-' }}
                                </th>
                                <th class="font-weight-normal">
                                    {{ number_format($order->coupon_1_price + $order->coupon_2_price + $order->coupon_3_price) }} 円
                                </th>
                                <th class="font-weight-normal">
                                    @if ($order->is_minute_add)
                                        有り
                                    @else 無し
                                    @endif
                                </th>
                                <th class="font-weight-normal">
                                    @if ($order->option_parent_id)
                                        {{ $order->option_parent_name }} {{ $order->option_sub_name ? '('.$order->option_sub_name.')' : '' }}
                                    @else 無し
                                    @endif
                                </th>
                                <th class="font-weight-normal">
                                    @if ($order->type_pay == Orders::TYPE_PAYMENT_CASH)
                                    現金
                                    @else カード
                                    @endif
                                </th>
                                <th class="font-weight-normal">
                                    <span id="total_order_price_{{ $order->id }}" data-price="{{ $totalOrderPriceShow }}">{{ number_format($totalOrderPriceShow) }} 円</span>
                                </th>
                                <th class="font-weight-normal">
                                    <span id="cast_price_{{ $order->id }}" data-price="{{ $order->price_cast }}">{{ number_format($order->price_cast) }} 円</span>
                                </th>
                                <th class="font-weight-normal">
                                    <span id="price_for_shop_{{ $order->id }}">{{ number_format($totalOrderPrice - $order->price_cast) }} 円</span>
                                </th>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>
        <input name="popup" value="{{ session('msg') ? session('msg') : '' }}" type="hidden">
    </div>
@endsection

@section('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        var dateNowPicker = @json($dateSelect->toDateString());
        var order = null;
        const NO_HAPPEN_YET = @json($mOrder::STATUS_NO_HAPPEN_YET);
        const START = @json($mOrder::STATUS_HAPPENING);
        const EXTENSION = @json($mOrder::STATUS_EXTENSION);
        const FINSH = @json($mOrder::STATUS_FINISH);
        var castIds = @json($castIds);
        var minuteExtension = @json($minuteExtension);
        var priceExtension = @json($priceExtension);
        var priceCastExtension = @json($priceCastExtension);
    </script>
    <script src="{{ assetMix('js/page_admin_manage_status.js') }} "></script>
@endsection
