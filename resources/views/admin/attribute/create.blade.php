@extends('admin.layouts.app')

@section('title', 'キャスト質問項目登録')

@section('css')
<link rel="stylesheet" href="{{ asset('css/admin.add.edit.attribute.css') }}" type="text/css">
@endsection

@section('adminPageName')
<h2 class="page-name-text">キャスト管理</h2>
@endsection

@section('pageNameBreadcrumb')
<div class="breadcrumb">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
            <li class="breadcrumb-item active" aria-current="page">キャスト質問項目登録</li>
        </ol>
    </nav>
</div>
@endsection

@section('content')
<div class="add-edit-attribute" id="add-manage-attribute">
    <div class="row">
        <div class="col-md-12">
            <div class="group-button-top">
                @include('admin.manage_cast.group_button')
            </div>
            <form action="{{ route('admin.cast.attribute.store') }}" method="POST" id="form-add-attribute">
                @csrf
                <div class="radio-status">
                    <label class="title">この質問項目を</label>
                    <div class="choose-status">
                        <div class="status1">
                            <input type="radio" name="status" value="1"
                                {{ old('status') === 1 || old('status') === null ? 'checked' : '' }}>
                            <label class="title1">表示</label>
                        </div>
                        <div class="status1">
                            <input type="radio" name="status" value="0" {{ old('status') === 0 ? 'checked' : '' }}>
                            <label class="title1">非表示</label>
                        </div>
                    </div>
                </div>
                <div class="row form-input">
                    <div class="col-sm-6 question">
                        <label>質問事項&ensp;</label>
                        <input type="text" name="name" value="{{ old('name') }}" class="input-question form-control"
                            maxlength="255"
                            oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                        <div class="alert alert-danger">{{ $errors->first('name') }}</div>
                    </div>
                    <div class="col-sm-10 answer">
                        <div class="col-sm-6">
                            @for ($j = 0; $j < 6; $j++)
                                <div class="col-sm-12">
                                    @php $index = $j + 1; @endphp
                                    <label>選択項目 {{ $index }}&ensp;</label>
                                    <input type="text" class="form-control" name="answer[{{ $index }}]"
                                        value="{{ old("answer.$index") }}" class="input-question" maxlength="1000"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                </div>
                            @endfor
                        </div>
                        <div class="col-sm-6">
                            @for ($j = 0; $j < 6; $j++)
                                <div class="col-sm-12">
                                    @php $index = 6 + $j + 1; @endphp
                                    <label>選択項目 {{ $index }}&ensp;</label>
                                    <input type="text" class="form-control" name="answer[{{ $index }}]"
                                        value="{{ old("answer.$index") }}" class="input-question" maxlength="1000"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                </div>
                            @endfor
                        </div>
                    </div>
                    <div class="choose-type">
                        <div class="text">質問項目に対してのタイプ選択</div>
                        <div class="col-sm-12">
                            <div class="col-sm-2">
                                <input type="radio" name="type" value="1" {{ old('type') == 1 ? 'checked' : '' }}
                                    checked>
                                <label>チェックボックス</label>
                            </div>
                            <div class="col-sm-2">
                                <input type="radio" name="type" value="2" {{ old('type') == 2 ? 'checked' : '' }}>
                                <label>ラジオボタン</label>
                            </div>
                            <div class="col-sm-2">
                                <input type="radio" name="type" value="3" {{ old('type') == 3 ? 'checked' : '' }}>
                                <label>プルダウン</label>
                            </div>
                            <div class="col-sm-2">
                                <input type="radio" name="type" value="4" {{ old('type') == 4 ? 'checked' : '' }}
                                    id='textarea'>
                                <label>テキストエリア</label>
                            </div>
                            <div class="col-sm-2">
                                <input type="radio" name="type" value="5" {{ old('type') == 5 ? 'checked' : '' }}
                                    id='textbox'>
                                <label>テキストボックス</label>
                            </div>
                        </div>
                        <div class="note">※ テキストエリア、テキストボックスを選択される場合は、「選択項目」のランは空白で登録してください。</div>
                    </div>
                </div>
                <div class="button-submit">
                    <button class="btn btn-submit" type="button">登録</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ assetMix('js/page_admin_cast_attribute.js') }} "></script>
@endsection
