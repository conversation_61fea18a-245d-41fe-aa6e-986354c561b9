<div class="btn-group">
    <a href="{{ route('admin.cast.index') }}"
        class="btn btn-ct-primary {{ Request::routeIs('admin.cast.index') ? 'active-primary' : '' }} btn-1"
        role="button">キャスト管理トップ</a>
    <a href="{{ $PERMISSION_READ_ONLY ? '' : route('admin.cast.create') }}"
        class="btn btn-ct-primary {{ Request::routeIs('admin.cast.create') ? 'active-primary' : '' }} btn-1 {{ $PERMISSION_READ_ONLY ? 'disabled' : '' }}"
        role="button">キャスト登録</a>
    <a href="{{ $PERMISSION_READ_ONLY ? '' : route('admin.cast.arrange') }}"
        class="btn btn-ct-primary {{ Request::routeIs('admin.cast.arrange') || (request()->is('admin/beppin_h/cast/*/edit') && !request()->is('admin/beppin_h/cast/attribute/*/edit')) ? 'active-primary' : '' }} btn-1 {{ $PERMISSION_READ_ONLY ? 'disabled' : '' }}"
        role="button">キャスト編集</a>
    <a href="#"
        class="btn btn-ct-primary btn-1 {{ request()->is('admin/beppin_h/cast/*/schedule') ? 'active-primary' : 'disabled' }} {{ $PERMISSION_READ_ONLY ? 'disabled' : '' }}"
        role="button">キャスト出勤登録</a>
    <a href="{{ route('admin.cast.achievement') }}"
        class="btn btn-ct-primary {{ Request::routeIs('admin.cast.achievement') ? 'active-primary' : '' }} btn-1"
        role="button">キャスト成績表</a>
</div>
<div class="btn-group">
    <a href="{{ route('admin.cast.attribute.index') }}"
        class="btn btn-ct-primary {{ Request::routeIs('admin.cast.attribute.index') ? 'active-primary' : '' }} btn-2"
        role="button">キャスト項目管理トップ</a>
    <a href="{{ $PERMISSION_READ_ONLY ? '' : route('admin.cast.attribute.create') }}"
        class="btn btn-ct-primary  {{ Request::routeIs('admin.cast.attribute.create') ? 'active-primary' : '' }}  btn-2 {{ $PERMISSION_READ_ONLY ? 'disabled' : '' }}"
        role="button">キャスト質問項目登録</a>
    <a href="#"
        class="btn btn-ct-primary btn-2 {{ request()->is('admin/beppin_h/cast/attribute/*/edit') ? 'active-primary' : 'disabled' }} {{ $PERMISSION_READ_ONLY ? 'disabled' : '' }}"
        role="button">キャスト質問項目編集</a>
</div>
