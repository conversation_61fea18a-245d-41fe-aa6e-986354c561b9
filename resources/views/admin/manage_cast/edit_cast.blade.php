@inject('modelCoupon','App\Models\Coupon')
@inject('attModel', 'App\Models\Attribute')
@inject('cstCast', 'App\Constants\Cast')
@php
    use Carbon\Carbon;
@endphp
@extends('admin.layouts.app')

@section('title', 'キャスト編集')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin.add.edit.cast.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2 class="page-name-text">キャスト管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">キャスト編集</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="manage-add-edit-cast" id="edit-manage-cast">
        <div class="row">
            <div class="col-md-12">
                <div class="group-button-top">
                    @include('admin.manage_cast.group_button')
                </div>
                <form method="POST" action="{{ route('admin.cast.update', $cast->id) }}" id="form-edit-cast">
                    @csrf
                    <input type="hidden" name="_method" value="PUT">
                    <input name="cast_id" value="{{ $cast->id }}" type="hidden">
                    <input name="status" value="{{ old('status', $cast->status) }}" type="hidden">
                    <input name="date-create" value="{{ $cast->created_at }}" type="hidden">
                    <div class="btn-status">
                        <button class="btn btn-ct-primary btn-2" type="button" id="status_show">表示</button>
                        <button class="btn btn-ct-primary btn-2" type="button" id="status_not_show">非表示</button>
                    </div>
                    <div class="row form-picture">
                        <div class="col-sm-3">
                            <div class="cast-item-1">
                                <?php $image = $cast->images()->first(); ?>
                                <div class="image-cast">
                                    <img src="{{ old('image.0', isset($image->path) ? asset($image->path) : '') }}"
                                        class="img-cast">
                                </div>
                                <div class="information-cast">
                                    <p> {{ $cast->name_cast }} ({{ $cast->age }}歳)</p>
                                    <p> T : {{ $cast->height }} cm</p>
                                    <div class="body-measurements">
                                        <p>B : {{ $cast->chest }}cm ({{ $cast->cup }} cup)</p>
                                        <p>W : {{ $cast->waist }}cm </p>
                                        <p>H : {{ $cast->hips }}cm</p>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-danger">{{ $errors->first('image.0') }}</div>
                        </div>
                        <div class="row col-sm-9 image-upload">
                            @for ($i = 0; $i < 2; $i++)
                                <div class="col-sm-12">
                                    @for ($j = 0; $j < 5; $j++)
                                        @php $index = $i * 5 + $j @endphp
                                        <div class="col-sm-2 form-upload">
                                            <input class="link-img" name="image[{{ $i * 5 + $j }}]"
                                                value="{{ old("image.$index", array_key_exists($index, $listImage) ? asset($listImage[$index]) : '') }}"
                                                readonly>
                                            @if (!$PERMISSION_READ_ONLY)
                                                <div class="delete-upload hidden">x</div>
                                            @endif
                                            @php
                                                $img = '';
                                                if (old("image.$index")) {
                                                    $img = asset(old("image.$index"));
                                                }
                                                if (array_key_exists($index, $listImage)) {
                                                    $img = asset($listImage[$index]);
                                                }
                                            @endphp
                                            <img src="{{ $img }}"
                                                class="{{ old("image.$index", array_key_exists($index, $listImage) ? asset($listImage[$index]) : '') ? '' : 'hidden' }}">
                                            <input type="file" class="hidden">
                                            <div class="alert alert-upload"></div>
                                            <button type="button" class="btn btn-sm btn-ct-primary btn-upload">登録</button>
                                        </div>
                                    @endfor
                                </div>
                            @endfor
                        </div>
                    </div>
                    <div class="row date">
                        <div class="col-sm-6 date-cast">
                            登録日： &ensp;<div class="row date-create"></div>
                        </div>
                        <div class="col-sm-6 cast-id">
                            ID：{{ $cast->id }}
                        </div>
                    </div>
                    <div class="select-datetime">
                        <div class="datetime-show">
                            <label class="time-show">公開日設定: &ensp;</label>
                            @if (old('year_public') || !isset($date_public))
                                @include('views.select_time',[
                                'type'=>'public',
                                'year' => old('year_public'),
                                'month' => old('month_public'),
                                'day' => old('day_public'),
                                ])
                            @else
                                @include('views.select_time',[
                                'type'=>'public',
                                'year' => $date_public->year,
                                'month' => $date_public->month,
                                'day' => $date_public->day,
                                ])
                            @endif
                            @php
                                $errorDatePublic = '';
                                if ($errors->first('day_public')) {
                                    $errorDatePublic = $errors->first('day_public');
                                }
                                if ($errors->first('month_public')) {
                                    $errorDatePublic = $errors->first('month_public');
                                }
                                if ($errors->first('year_public')) {
                                    $errorDatePublic = $errors->first('year_public');
                                }
                            @endphp
                            <div class="alert alert-danger">{{ $errorDatePublic }}</div>
                        </div>
                        <div class="row datetime-start">
                            <div class="col-sm-7 select-date">
                                <label class="time-show">入店日: &ensp;</label>
                                @if (old('year_start'))
                                    @include('views.select_time',[
                                    'type'=>'start',
                                    'year' => old('year_start'),
                                    'month' => old('month_start'),
                                    'day' => old('day_start'),
                                    ])
                                @else
                                    @include('views.select_time',[
                                    'type'=>'start',
                                    'year' => $date_start->year,
                                    'month' => $date_start->month,
                                    'day' => $date_start->day,
                                    ])
                                @endif
                                @php
                                    $errorDateStart = '';
                                    if ($errors->first('day_start')) {
                                        $errorDateStart = $errors->first('day_start');
                                    }
                                    if ($errors->first('month_start')) {
                                        $errorDateStart = $errors->first('month_start');
                                    }
                                    if ($errors->first('year_start')) {
                                        $errorDateStart = $errors->first('year_start');
                                    }
                                @endphp
                                <div class="alert alert-danger">{{ $errorDateStart }}</div>
                            </div>
                            <div class="col-sm-5 show-time">
                                <div class="text">
                                    新人期間： {{ formatDateJp($date_start->addMonths($cstCast::MONTH_FOR_NEW_CAST)) }} まで
                                </div>
                                <div class="note">※ 入店日より{{ $cstCast::MONTH_FOR_NEW_CAST }}ヶ月間は、新人扱いになります。
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-info">
                        <div class="col-sm-6 info-1">
                            <div class="input-info">
                                <label class="label">キャスト名 &ensp;</label>
                                <input type="text" class="form-control" name="name_cast"
                                    value="{{ old('name_cast', $cast->name_cast) }}" data-type="name">
                                <div class="note">※キャストの名前は、ひらがなで記入してください。</div>
                                <div class="alert alert-danger">{{ $errors->first('name_cast') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">年齢 &ensp;</label>
                                <input type="text" class="form-control" name="age" value="{{ old('age', $cast->age) }}"
                                    data-type="currency" maxlength="2" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                &ensp;歳
                                <div class="alert alert-danger">{{ $errors->first('age') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">身長 &ensp;</label>
                                <input type="text" class="form-control" name="height"
                                    value="{{ old('height', $cast->height) }}" data-type="currency" maxlength="3" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                &ensp;cm
                                <div class="alert alert-danger">{{ $errors->first('height') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">バスト &ensp;</label>
                                <input type="text" class="form-control" name="chest"
                                    value="{{ old('chest', $cast->chest) }}" data-type="currency" maxlength="3" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;cm
                                <div class="alert alert-danger">{{ $errors->first('chest') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">Cup &ensp;</label>
                                <input type="text" class="form-control" name="cup" value="{{ old('cup', $cast->cup) }}"
                                    maxlength="10"
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;cup
                                <div class="alert alert-danger">{{ $errors->first('cup') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">ウエスト &ensp;</label>
                                <input type="text" class="form-control" name="waist"
                                    value="{{ old('waist', $cast->waist) }}" data-type="currency" maxlength="3" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;cm
                                <div class="alert alert-danger">{{ $errors->first('waist') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">ヒップ &ensp;</label>
                                <input type="text" class="form-control" name="hips"
                                    value="{{ old('hips', $cast->hips) }}" data-type="currency" maxlength="3" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;cm
                                <div class="alert alert-danger">{{ $errors->first('hips') }}</div>
                            </div>
                            <div class="input-info input-2">
                                <label class="label">マット &ensp;</label>
                                <div class="col-sm-6 check-box">
                                    <input class='input-radio' type="radio" name="matto_play" value="1"
                                        {{ old('matto_play', $cast->matto_play) ? 'checked' : '' }}>
                                    <label>&nbsp;可</label>
                                    <input class='input-radio' type="radio" name="matto_play" value="0"
                                        {{ old('matto_play', $cast->matto_play) ? '' : 'checked' }}>
                                    <label>&nbsp;不可</label>
                                </div>
                            </div>
                            <div class="input-info input-2">
                                <label class="label">外国人 &ensp;</label>
                                <div class="col-sm-6 check-box">
                                    <input class='input-radio' type="radio" name="is_foreigner" value="1"
                                        {{ old('is_foreigner', $cast->is_foreigner) ? 'checked' : '' }}>
                                    <label>&nbsp;可</label>
                                    <input class='input-radio' type="radio" name="is_foreigner" value="0"
                                        {{ old('is_foreigner', $cast->is_foreigner) ? '' : 'checked' }}>
                                    <label>&nbsp;不可</label>
                                </div>
                            </div>
                            <div class="input-info">
                                <label class="label">本名 &ensp; </label>
                                <input type="text" class="form-control" name="name"
                                    value="{{ old('name', $cast->name) }}" data-type="name">
                                <div class="alert alert-danger">{{ $errors->first('name') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">メールアドレス &ensp; </label>
                                <input type="text" class="form-control" name="email"
                                    value="{{ old('email', $cast->email) }}" data-type="email" 　
                                    pattern="^[A-Za-z0-9][A-Za-z0-9_-!¡?÷?¿/\\+=@#$%ˆ&*(.){}|~<>;:[\]]*$" maxlength="30"
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                <div class="alert alert-danger">{{ $errors->first('email') }}</div>
                            </div>
                            <div class="input-info">
                                <label class="label">電話番号 &ensp;</label>
                                <input type="text" class="form-control" name="phone"
                                    value="{{ old('phone', $cast->phone) }}" data-type="phone" maxlength="11"
                                    placeholder="0xx-xxxx-xxxx"
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                <div class="alert alert-danger">{{ $errors->first('phone') }}</div>
                            </div>
                            <div class="input-info input-2">
                                <label class="label">寮への入居 &ensp;</label>
                                <div class="col-sm-6 check-box">
                                    <input class='input-radio' id="dormitory1" type="radio" name="dormitory" value="1"
                                        {{ old('dormitory', $cast->dormitory) ? 'checked' : '' }}>
                                    <label>&nbsp;有り</label>
                                    <input class='input-radio' id="dormitory0" type="radio" name="dormitory" value="0"
                                        {{ old('dormitory', $cast->dormitory) ? '' : 'checked' }}>
                                    <label>&nbsp;無し</label>
                                </div>
                            </div>
                            <div class="input-info input-3 {{ old('dormitory', $cast->dormitory) ? '' : 'hidden' }}"
                                id="dormitory_price">
                                <label class="label">寮費 &ensp;</label>
                                <input type="text" class="form-control" name="dormitory_price"
                                    value="{{ old('dormitory_price', $cast->dormitory_price) }}" data-type="currency"
                                    maxlength="9" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円/日
                                <div class="alert alert-danger">{{ $errors->first('dormitory_price') }}</div>
                                <div class="note">※ 編集完了後、既に、売上管理に表記されている料金は、凍結されます。</div>
                            </div>
                            <div class="input-info input-2">
                                <label class="label">モニターチェック &ensp;</label>
                                <div class="col-sm-6 check-box">
                                    <input class='input-radio' type="radio" name="monitor_check" value="1"
                                        {{ old('monitor_check', $cast->monitor_check) ? 'checked' : '' }}>
                                    <label>&nbsp;有り</label>
                                    <input class='input-radio' type="radio" name="monitor_check" value="0"
                                        {{ old('monitor_check', $cast->monitor_check) ? '' : 'checked' }}>
                                    <label>&nbsp;無し</label>
                                </div>
                            </div>
                            <div class="input-info input-3">
                                <label class="label">指名料金 &ensp;</label>
                                <input type="text" class="form-control" name="name_fee"
                                    value="{{ old('name_fee', $cast->name_fee) }}" data-type="currency" maxlength="9"
                                    min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                <div class="note">※ 編集完了後、既に、売上管理に表記されている料金は、凍結されます。</div>
                            </div>
                            <div class="input-info input-3">
                                <label class="label">ネット指名料金 &ensp;</label>
                                <input type="text" class="form-control" name="net_name_fee"
                                    value="{{ old('net_name_fee', $cast->net_name_fee) }}" data-type="currency"
                                    maxlength="9" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                <div class="note">※ 編集完了後、既に、売上管理に表記されている料金は、凍結されます。</div>
                            </div>
                            <div class="input-info input-3">
                                <label class="label">キャスト直指名料金 &ensp;</label>
                                <input type="text" class="form-control" name="cast_name_fee"
                                    value="{{ old('cast_name_fee', $cast->cast_name_fee) }}" data-type="currency"
                                    maxlength="9" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                <div class="note">※ 編集完了後、既に、売上管理に表記されている料金は、凍結されます。</div>
                            </div>
                            <div class="input-info input-3">
                                <label class="label">キャストインターバル &ensp;</label>
                                <input type="text" class="form-control" name="interval"
                                    value="{{ old('interval', $cast->interval) }}" data-type="currency" maxlength="9"
                                    min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;分
                                <div class="note">※ 編集完了後、既に、売上管理に表記されている料金は、凍結されます。</div>
                            </div>
                            <div class="input-info input-3">
                                <label class="label">日々の雑費 &ensp;</label>
                                <input type="text" class="form-control" name="expenses"
                                    value="{{ old('expenses', $cast->expenses) }}" data-type="currency" maxlength="3"
                                    min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;%
                                <div class="alert alert-danger">{{ $errors->first('expenses') }}</div>
                                <div class="note">※ キャストの売上から〇%引かれます。</div>
                                <div class="note">※ 編集完了後、既に、売上管理に表記されている料金は、凍結されます。</div>
                            </div>
                            <div class="input-info">
                                <label class="label">適用オプション &ensp; </label>
                                <div class="row list-checkbox-option">
                                    @foreach ($listOptionParent as $key => $optionParent)
                                        <div class="option-parent col-sm-4">
                                            <input class='input-radio' type="checkbox"
                                                name="optionParent[{{ $key }}]" value="{{ $optionParent->id }}"
                                                {{ old("optionParent.$key") ? 'checked' : '' }}
                                                {{ !old("optionParent.$key") && in_array($optionParent->id, $listOptionOfCast) ? 'checked' : '' }}>
                                            <label>{{ $optionParent->name }}</label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="input-info">
                                <label class="label">適切割引 &ensp; </label>
                                <div class="row list-checkbox-option">
                                    @foreach ($listCoupon as $key => $coupon)
                                        <div class="option-parent col-sm-4">
                                            <input class='input-radio' type="checkbox" name="coupon[{{ $key }}]"
                                                value="{{ $coupon->id }}" {{ old("coupon.$key") ? 'checked' : '' }}
                                                {{ !old("coupon.$key") && in_array($coupon->id, $listCouponOfCast) ? 'checked' : '' }}
                                                {{ $coupon->id == 1 && now() < $date_start ? 'disabled' : '' }}>
                                            <label>{{ $coupon->name }}</label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 attribute">
                            @php $i = 1; @endphp
                            @foreach ($listAttribute as $attribute)
                                <div class="item-attribute">
                                    <div class="question">
                                        {{ 'Q' . $i . ': ' }}<div class="text">&nbsp;{{ $attribute->name }}
                                        </div>
                                        <img src="{{ asset('images/answer.png') }}" data-toggle="modal"
                                            data-target="#myModal{{ $i }}">
                                    </div>
                                    <div class="answer">
                                        @if ($attribute->type == $attModel::TYPE_TEXTAREA)
                                            <pre class="text fill-text" readonly>回答:</pre>
                                        @else
                                            <div class="text fill-text">回答:</div>
                                        @endif
                                    </div>
                                    <div class="modal popup-answer" id="myModal{{ $i }}">
                                        <div class="modal-dialog modal-lg">
                                            <div class="container">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <div class="content-popup">回答項目</div>
                                                    </div>
                                                    <div class="clearfix">
                                                        <div class="text-question">
                                                            {{ 'Q' . $i . ': ' }}<div class="text">
                                                                &nbsp;{{ $attribute->name }}</div>
                                                        </div>
                                                        <div class="answer">
                                                            <div class="text">回答: </div>
                                                            @if (!count($attribute->attributeItem()->get()) && ($attribute->type != $attModel::TYPE_TEXTAREA && $attribute->type != $attModel::TYPE_TEXTBOX))
                                                                <div class="note">※回答選択肢は無し</div>
                                                            @else
                                                                @include('admin.manage_cast.modal_attribute', [
                                                                'attribute' => $attribute,
                                                                'old' => old("answer.$attribute->id"),
                                                                'attributeOfCast' => isset($attributeOfCast) ?
                                                                $attributeOfCast : []
                                                                ])
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button"
                                                            class="btn btn-primary btn-save{{ $attribute->type }}"
                                                            data-dismiss="modal">保存</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="answer-hidden">
                                        @if ($attribute->type == $attModel::TYPE_CHECKBOX || $attribute->type == $attModel::TYPE_RADIO)
                                            @foreach ($attribute->attributeItem()->get() as $item)
                                                <input type="hidden"
                                                    name="answer[{{ $attribute->id }}][{{ $item->id }}]"
                                                    id="{{ $item->id }}" value="">
                                            @endforeach
                                        @else
                                            <input type="hidden" name="answer[{{ $attribute->id }}]" value="">
                                        @endif
                                    </div>
                                </div>
                                @php $i++; @endphp
                            @endforeach
                        </div>
                    </div>
                    <div class="select-datetime">
                        <div class="datetime-out">
                            <label class="time-out">退店日: &ensp;</label>
                            @if (old('year_end') || !isset($date_end))
                                @include('views.select_time',[
                                'type'=>'end',
                                'year' => old('year_end'),
                                'month' => old('month_end'),
                                'day' => old('day_end'),
                                ])
                            @else
                                @include('views.select_time',[
                                'type'=>'end',
                                'year' => $date_end->year,
                                'month' => $date_end->month,
                                'day' => $date_end->day,
                                ])
                            @endif
                            @php
                                $errorDateEnd = '';
                                if ($errors->first('day_end')) {
                                    $errorDateEnd = $errors->first('day_end');
                                }
                                if ($errors->first('month_end')) {
                                    $errorDateEnd = $errors->first('month_end');
                                }
                                if ($errors->first('year_end')) {
                                    $errorDateEnd = $errors->first('year_end');
                                }
                            @endphp
                            <div class="alert alert-danger">{{ $errorDateEnd }}</div>
                            <div class="note">※
                                退店日は、記録として残るだけなので、データを残す場合は、「非表示」ボタンをクリックしください。完全に削除する場合は、削除へチェックを入れてください。
                            </div>
                        </div>
                        <div class="button-submit">
                            <div class="checkbox">
                                <input type="checkbox" id="check-delete">&ensp;削除
                            </div>
                            <button type="button" class="btn btn-ct-primary btn-submit">編集完了</button>
                        </div>
                </form>
            </div>
            @if (count($orders) > 0)
                <div class="table-list table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>来店日</th>
                                <th>来店時間</th>
                                <th>コース</th>
                                <th>種別</th>
                                <th>お客様名</th>
                                <th>電話番号</th>
                                <th>割引</th>
                                <th>料金</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (isset($orders) && $orders)
                                @foreach ($orders as $key => $order)
                                    <tr>
                                        <td>{{ $key + $orders->firstItem() }}</td>
                                        <td>{{ formatDateJp($order->date) }}
                                        </td>
                                        @php
                                            $timeStart = explode(":", Carbon::parse($order->actual_date_start ? $order->actual_date_start : $order->date_start)->format('H:i'));
                                            $timeEnd = explode(":", Carbon::parse($order->actual_date_end ? $order->actual_date_end : $order->date_end)->format('H:i'));
                                            if ($timeStart[0] < CLOSE_HOURS - 24) {
                                                $timeStart[0] += 24;
                                            }
                                            if ($timeEnd[0] < CLOSE_HOURS - 24) {
                                                $timeEnd[0] += 24;
                                            }
                                            $startTime = implode(":", $timeStart);
                                            $endTime = implode(":", $timeEnd);
                                        @endphp
                                        <td>{{ $startTime }} ~ {{ $endTime }}
                                        </td>
                                        <td>{{ $order->course_name }}
                                        </td>
                                        <td>{{ $order->type_fee_text }}
                                        </td>
                                        <td>{{ $order->customer_name }}
                                        </td>
                                        <td style="{{ $order->reason_ng_customer ? 'color:red' : '' }}">{{ $order->customer_phone_number }}
                                        </td>
                                        <td>{{ $order->coupon_price ? number_format($order->coupon_price) : '0' }} 円</td>
                                        <td>{{ $order->total_price ? number_format($order->total_price) : '0' }} 円</td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                    <div class="col-md-12">
                        <div class="float-right">
                            {{ $orders->links() }}
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_admin_manage_cast.js') }} "></script>
@endsection
