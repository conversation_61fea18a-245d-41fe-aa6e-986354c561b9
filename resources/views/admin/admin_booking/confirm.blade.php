@extends('admin.layouts.app')

@section('title', '予約管理')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/confirm.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.breadcrumb.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2 class="page-name-text">予約管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">予約管理</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="confirm-page admin-booking" id="page-booking-confirm">
        <div class="container">
            <form method="POST" action="{{ route('admin.booking.confirm.store') }}">
                @csrf
                <input type="hidden" name="total_point" value={{ $customer_point }}>
                <input type="hidden" name="total_price" value="{{ $total }}">
                <input type="hidden" name="point_price" value="{{ $pointPrice }}">
                <input type="hidden" name="price_point" value="{{ $pricePoint }}">
                <div class="head">
                    <div class="text">
                        <p>Q.下記予約内容でお問違いございませんか？</p>
                    </div>
                    <div class="label">
                        <button type="button" class="{{ $is_member ? 'member selected' : 'member' }}" id="click_member"
                            disabled>会員</button>
                        <button type="button" class="{{ !$is_member ? 'member selected' : 'member' }}" id="click_member2"
                            disabled>非会員</button>
                    </div>
                </div>
                <div class="row information">
                    <div class="col-sm-6 col-left">
                        <img src="{{ asset('images/background-confirm-left.png') }}" class="background">
                        <img src="{{ asset('images/layer-confirm-left.png') }}" class="layer-left">
                        <section class="spikes">
                            <div class="text-head">
                                予約内容
                            </div>
                            <div class="box-infor">
                                <div class="text-row">
                                    <input type="hidden" name="date_time" value="{{ $date->format('Y-m-d') }}">
                                    <div class="text">予約日</div>
                                    <div class="info" id="date-time"></div>
                                </div>
                                <div class="text-row">
                                    <div class="text">予約時間</div>
                                    @php
                                        $timeStart = sprintf('%02d', $dateStart->hour) . ':' . sprintf('%02d', $dateStart->minute);
                                        if ($dateStart->day != $date->day) {
                                            $timeStart = sprintf('%02d', $dateStart->hour + 24) . ':' . sprintf('%02d', $dateStart->minute);
                                        }
                                        $timeEnd = sprintf('%02d', $dateEnd->hour) . ':' . sprintf('%02d', $dateEnd->minute);
                                        if ($dateEnd->day != $date->day) {
                                            $timeEnd = sprintf('%02d', $dateEnd->hour + 24) . ':' . sprintf('%02d', $dateEnd->minute);
                                        }
                                    @endphp
                                    <div class="info">{{ $timeStart }} ~ {{ $timeEnd }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">予約方法</div>
                                    <div class="info">{{ $textMethod }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">キャスト名</div>
                                    <div class="info">{{ $cast->name_cast }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">コース</div>
                                    <div class="info">{{ $course->time }}分</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">スタート +</div>
                                    <div class="info">{{ $addTime ? '有り' : '無し' }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">キャストとのお遊び経験</div>
                                    <div class="info">{{ $firstTimeWithCast ? '無し' : '有り' }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">オプション</div>
                                    <div class="info">{{ $nameOption }}</div>
                                </div>
                                <div class="text-row text-coupon">
                                    <div class="text">割引</div>
                                    <div class="info">
                                        @if (!$listNameCoupon) 無し
                                        @else
                                            @foreach ($listNameCoupon as $nameCoupon)
                                                <div>{{ $nameCoupon ? $nameCoupon : '' }}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                                <div class="text-row-2">
                                    <div class="text">総額（オプション + 指名料 + 税）</div>
                                    <div class="info">{{ number_format($total) }} 円</div>
                                </div>
                            </div>
                    </div>
                    </section>

                    <div class="col-sm-6">
                        <img src="{{ asset('images/layer-confirm-right.png') }}" class="layer-left">
                        <section class="spikes-2">
                            <div class="text-head">
                                会員情報
                            </div>
                            <div class="box-infor">
                                <div class="text-row">
                                    <div class="text">会員番号</div>
                                    <div class="info">{{ $customer_code }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">お客様名</div>
                                    <div class="info">{{ $customer_name }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">電話番号</div>
                                    <div class="info">{{ $customer_phone }}</div>
                                </div>
                                <div class="text-row">
                                    <div class="text">生年月日</div>
                                    <div class="info">{{ $customer_birthday }}</div>
                                </div>
                                <div class="text-row-2">
                                    <div class="text">所持ポイント</div>
                                    <div class="info">
                                        {{ $customer_point ? number_format($customer_point) : '-' }} pt
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="spikes-3">
                            <div class="text-row">
                                <div class="text col-sm-6">ポイントを使用</div>
                                <div class="text col-sm-3">
                                    <div class="price" id="point-use">-
                                        {{ $is_member && $pointPrice ? number_format($pointUse / $pointPrice) : '' }}
                                        円</div>
                                </div>
                                <div class="info col-sm-3">
                                    <div class="price" id='total-price'>
                                        {{ $is_member ? number_format($totalPrice) : '-' }} 円</div>
                                </div>
                            </div>
                            <div class="text-row-2">
                                <div class="text col-sm-6">ポイントを貯める</div>
                                <div class="text col-sm-3" id="point-plus">
                                    {{ $is_member && $pricePoint ? '+' . number_format($total / $pricePoint) : '-' }} pt
                                </div>
                                <div class="info col-sm-3" id="total-point">
                                    {{ $is_member ? number_format($totalPoint) : '-' }} pt
                                </div>
                            </div>
                            <div class="text-row-3">
                                <button type="button" class="btn" id="use-point" data-toggle="modal"
                                    data-target="#myModal" {{ $is_member ? '' : 'disabled' }}>使う</button>
                            </div>
                        </section>
                    </div>
                </div>
                <div class="modal js-default-ok-popup" id="myModal">
                    <div class="modal-dialog modal-lg">
                        <div class="container">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <div class="content-popup">ポイント使用画面です</div>
                                </div>
                                <div class="clearfix">
                                    <div class="text-box">
                                        <div class="text">所持ポイント&emsp;&emsp;</div>
                                        <div class="point">{{ number_format($customer_point) }} pt</div>
                                    </div>
                                    <div class="text-box">使用するポイント数を入力してください</div>
                                    <input class="input-point form-control" type="text" name="point_use"
                                        data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    <div class="alert alert-danger"></div>
                                    <div class="text-box-2">
                                        <div class="text">ポイント使用後の料金&emsp;&emsp;</div>
                                        <div class="price" id="total-price-2">{{ number_format($total) }} 円
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row button-confirm">
                    <button type="submit" class="btn btn-confirm">決定</button>
                </div>
                @include('admin.admin_booking.steps', [
                'active'=> 5,
                'buttonNext' => false
                ])
                <input name="popup2" value="{{ session('popup') ? session('popup') : '' }}" type="hidden">
                <div class="overlay" id="overlay2"></div>
                <div id="popup2" class="popup">
                    <div class="container">
                        <div class="content-popup">
                            <strong>登録完了しました。</strong>
                        </div>
                        <div class="clearfix">
                            <a href="{{ route('admin.booking.order') }}" role="button" class="submitbtn">OK</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    </div>
@endsection

@section('appScript')
    <script src="{{ assetMix('js/page_cast.js') }} "></script>
    <script src="{{ assetMix('js/page_booking.js') }} "></script>
@endsection
