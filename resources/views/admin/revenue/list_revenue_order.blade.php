@php
use Carbon\Carbon;
$strTime = $type == 'date' ? 'd' : 'm';
$col = $type == 'date' ? 2 : 1;
$totalColumn = count($courses) + 8 + $col;
@endphp

<table class="table table-bordered table-shadown text-center">
    <thead>
        <tr>
            <th></th>
            @if ($type == 'date')
                <th></th>
            @endif
            @if (count($courses))
                <th colspan="{{ count($courses) }}">コース別件数</th>
            @endif
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
        </tr>
        <tr>
            <th>{{ $type == 'date' ? '日' : '月' }}</th>
            @if ($type == 'date')
                <th>曜日</th>
            @endif
            @forelse ($courses as $course)
                <th>{{ $course->name }}</th>
            @empty
            @endforelse
            <th>延長</th>
            <th>オプション</th>
            <th>来店顧客数</th>
            <th>割引額</th>
            <th>経常利益</th>
            <th>入金金額</th>
            <th>支払い金額</th>
            <th>売上</th>
        </tr>
    </thead>
    <tbody>
        @forelse ($arrDate as $keyDate)
            @php
                $cbDate = Carbon::parse($keyDate);
                $revenue = null;
                $revenueCourse = null;
                $priceOrders = 0;
                $priceOrderCast = 0;
                $priceOrdersCash = 0;
                $pricePaidForCast = 0;
                $priceDormitory = 0;
                $priceExpenses = 0;
                $priceLiving = 0; 
                $priceDecorateRoom = 0;
                if (isset($revenueGroupDate[$keyDate])) {
                    $revenue = $revenueGroupDate[$keyDate];
                    $priceOrders = $revenue->total_price_cash_orders;
                    $pricePaidForCast = $revenue->price_paid_for_cast;
                    $priceOrderCast = $revenue->total_price_cast;
                    $priceOrdersCash = $revenue->price_orders_cash;
                    $priceDormitory = $revenue->price_dormitory;
                    $priceExpenses =  $revenue->price_expenses;
                    $priceLiving = $revenue->price_living; 
                    $priceDecorateRoom = $revenue->price_decorate_room;
                }
                
                if (isset($revenueCourseGroupDate[$keyDate])) {
                    $revenueCourse = $revenueCourseGroupDate[$keyDate];
                }
                $chartRevenue[$keyDate] = $priceOrders - $priceOrderCast;
            @endphp
            <tr>
                <td>{{ $cbDate->format($strTime) }}</td>
                @if ($type == 'date')
                    <td>{{ strJPDaysOfWeek($cbDate) }}</td>
                @endif
                @forelse ($courses as $course)
                    <td>
                        {{ isset($revenueCourse[$course->id]) ? $revenueCourse[$course->id]->count_course : 0 }}
                    </td>
                @empty
                @endforelse
                <td>{{ $revenue ? $revenue->total_extension : 0 }}</td>
                <td>{{ $revenue ? $revenue->total_option : 0 }}</td>
                <td>{{ $revenue ? $revenue->total_customers : 0 }}</td>
                <td>¥{{ $revenue ? number_format($revenue->total_price_coupon, 1) : 0 }}</td>
                <td>¥{{ number_format(floatval($priceDormitory) + floatval($priceExpenses) + floatval($priceLiving) + floatval($priceDecorateRoom), 1) }}</td>
                <td>¥{{ number_format(floatval($priceOrdersCash) - floatval($pricePaidForCast), 1) }}</td>
                <td>¥{{ number_format($priceOrderCast, 1) }}</td>
                <td>¥{{ number_format($priceOrders - $priceOrderCast, 1) }}
                </td>
            </tr>
        @empty
            <tr>
                <td colspan="{{ $totalColumn }}" class="color-red">※該当なデータ無し </td>
            </tr>
        @endforelse
    </tbody>
</table>

@php
$arrKey = [];
for ($i = 0; $i < count($chartRevenue); $i++) {
    $arrKey[] = $i + 1;
}
$arrValue = array_values($chartRevenue);
@endphp

@section('scripts')
    <script>
        var dateNowPicker = @json($date->toDateString());
        var dataLabels = @json($arrKey);
        var dataValue = @json($arrValue);
    </script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="{{ assetMix('js/page_admin_revenue.js') }} "></script>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        var ctx = document.getElementById('userChart').getContext('2d');
        const labels = dataLabels;
        const data = {
            labels: labels,
            datasets: [{
                data: dataValue,
                borderColor: 'rgb(63, 115, 185)',
                borderWidth: 1,
                pointStyle: 'rectRot',
                pointRadius: 5,
                pointBorderColor: 'rgb(63, 115, 185)',
                backgroundColor: 'rgb(63, 115, 185',
            }]
        };

        const chart = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                interaction: {
                    intersect: false
                },
                plugins: {
                    legend: false
                },
                scales: {
                    y: {
                        ticks: {
                            callback: function(value, index, values) {
                                return '¥' + Intl.NumberFormat('en-US').format(value);
                            }
                        }
                    }
                }
            }
        });
    </script>
@endsection
