@inject('mUserRole', 'App\Models\UserRole')
@extends('admin.layouts.app')

@section('title', 'ユーザー管理')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin.users.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2>ユーザー管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.home.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">ユーザー管理</a></li>
                <li class="breadcrumb-item active" aria-current="page">ユーザー登録</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    @php
    if (Session::has('error_password') || $errors->any()) {
        $display = true;
    } else {
        $display = false;
    }
    @endphp
    <div class=" mt-3 manage-user create-user ">
        <h3 class="text"> 基本情報</h3>
        <form action="{{ route('admin.users.update', $user->id) }}" method="POST" id="form-edit-user">
            {{ method_field('PUT') }}
            @csrf
            <input type="hidden" name="id" class="form-control" value=" {{ $user->id }} ">
            <input type="hidden" name="change_password" class="form-control" value="0">
            <div class="row">
                <div class="form-group col-md-6">
                    <label for=""> 名前 </label>
                    <input type="text" name="name" class="form-control"
                        value=" {{ old('name') ? old('name') : $user->name }} ">
                    @error('name')
                        <div class="alert alert-danger">{{ $message }}</div>
                    @enderror
                </div>
                <div class="form-group col-md-6">
                    <label for="">ユーザー名</label>
                    <input type="text" class="form-control" disabled value="{{ $user->email }} ">
                </div>
            </div>
            <p class="change-password">
                パスワード変更
            </p>
            <div class="form-change-password">
                @if ($user->id == 1)
                    <div class="row">
                        <div class="form-group col-md-6 password">
                            <label for="">現在のパスワード</label>
                            <input type="password" name="password_old" class="form-control"
                                value="{{ old('password_old') ? old('password_old') : '' }}" autocomplete="new-password">
                            <i class="fa fa-eye" aria-hidden="true"></i>
                            <i class="fa fa-eye-slash" aria-hidden="true"></i>
                            <p class="text-danger" id="error-password-old">
                                @if (Session::has('error_password'))
                                    {{ Session::get('error_password') }}
                                @endif
                            </p>
                        </div>
                    </div>
                @endif
                <div class="row">
                    <div class="form-group col-md-6 password">
                        <label for="">新しいパスワード</label>
                        <input type="password" name="password_new" class="form-control"
                            value="{{ old('password_new') ? old('password_new') : '' }}">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                        @error('password_new')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group col-md-6 password">
                        <label for="">新しいパスワード（再入力）</label>
                        <input type="password" name="password_confirm" class="form-control"
                            value="{{ old('password_confirm') ? old('password_confirm') : '' }}">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                        @error('password_confirm')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            @if ($user->id != 1)
                <h3 class="text-center mb-3">権限割り当て選択</h3>
                <div class="table-permission">
                    <div class="table-list table-responsive">
                        <table class="table borderless">
                            @forelse ($groupPageRoute as $keyPage => $item)
                                <tr>
                                    @php
                                        $type = $mUserRole::TYPE_ACCESS;
                                        if (isset($dataPermission[$keyPage])) {
                                            $type = $dataPermission[$keyPage]['type'];
                                        }
                                    @endphp
                                    <td>{{ $item['label'] }}</td>
                                    <td class="type-permission">
                                        <div>
                                            <input type="radio" name="permission_{{ $keyPage }}"
                                                id="inputAccess{{ $keyPage }}"
                                                value="{{ $mUserRole::TYPE_ACCESS }}" @php
                                                    $checked = '';
                                                    if (old('permission_{{ $keyPage }}')) {
                                                        if (old('permission_{{ $keyPage }}') == $mUserRole::TYPE_ACCESS) {
                                                            $checked = 'checked';
                                                        } elseif (!old('permission_{{ $keyPage }}') && $type == $mUserRole::TYPE_ACCESS) {
                                                            $checked = 'checked';
                                                        }
                                                    } else {
                                                        $checked = 'checked';
                                                    }
                                                @endphp
                                                {{ $checked }}>
                                            <label for="inputAccess{{ $keyPage }}">アクセス不可</label>
                                        </div>
                                    </td>
                                    <td class="type-permission">
                                        <div>
                                            <input type="radio" name="permission_{{ $keyPage }}"
                                                value="{{ $mUserRole::TYPE_VIEW }}" id="inputView{{ $keyPage }}"
                                                {{ old("permission_$keyPage") == $mUserRole::TYPE_VIEW ? 'checked' : (!old("permission_$keyPage") && $type == $mUserRole::TYPE_VIEW ? 'checked' : '') }}>
                                            <label for="inputView{{ $keyPage }}">閲覧のみ</label>
                                        </div>
                                    </td>
                                    <td class="type-permission">
                                        <div>
                                            <input type="radio" name="permission_{{ $keyPage }}"
                                                value="{{ $mUserRole::TYPE_EDIT }}" id="inputEdit{{ $keyPage }}"
                                                {{ old("permission_$keyPage") == $mUserRole::TYPE_EDIT ? 'checked' : (!old("permission_$keyPage") && $type == $mUserRole::TYPE_EDIT ? 'checked' : '') }}>
                                            <label for="inputEdit{{ $keyPage }}">編集</label>
                                        </div>
                                    </td>
                                </tr>
                            @empty

                            @endforelse
                        </table>
                    </div>
                </div>
            @endif

        </form>
        <div class="button-form text-center">
            <a href="{{ route('admin.users.index') }}" class="btn  btn-cancel">キャンセル</a>
            <button type="submit" class="btn btn-save btn-save-edit">保存</button>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_admin_user.js') }} "></script>
    <script>
        var display = @json($display);
    </script>
@endsection
