<!-- Modal -->
<div class="modal js-default-ok-popup fade dbclick-un-radio couponModal" id="couponModal{{ $orders->id }}"
    attr-order-id="{{ $orders->id }}" tabindex="-1" role="dialog" aria-labelledby="couponModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5 class="modal-title text-center" id="couponModalLabel">割引追加</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item px-0">
                        <a class="btn collapsed" data-toggle="collapse" href="#collapseExample1" role="button"
                            aria-expanded="true" aria-controls="collapseExample1">
                            割引<span class="mr-3">
                                @if (isset($couponNormals) && count($couponNormals))
                                    <i class="fa fa-caret-down" aria-hidden="true"></i>
                                @endif
                            </span>
                        </a>
                        <div class="collapse" id="collapseExample1">
                            <div class="card card-body mt-2">
                                @if (isset($couponNormals) && count($couponNormals))
                                    @foreach ($couponNormals as $item)
                                        <div class="radio">
                                            <label for="cradio{{ $item->id }}{{ $orders->id }}">
                                                <span>{{ $item->name }}</span>
                                            </label>
                                            @if (!empty($orderCouponNormal) && $orderCouponNormal->pivot->coupon_id == $item->id)
                                                <input type="radio" id="cradio{{ $item->id }}{{ $orders->id }}"
                                                    class="radio style-2" value="{{ $item->id }}"
                                                    name="coupon_normal_{{ $orders->id }}"
                                                    attr-name="{{ $orderCouponNormal->pivot->name }}"
                                                    attr-share-ids="{{ $orderCouponNormal->shareCouponIds($orders->course_id, $orders->cast_id) }}"
                                                    attr-price="{{ $orderCouponNormal->pivot->price }}" checked />
                                            @else
                                                <input type="radio" id="cradio{{ $item->id }}{{ $orders->id }}"
                                                    class="radio style-2" value="{{ $item->id }}"
                                                    name="coupon_normal_{{ $orders->id }}"
                                                    attr-name="{{ $item->name }}"
                                                    attr-share-ids="{{ $item->share_coupon_ids }}"
                                                    attr-price="{{ $item->price }}" />
                                            @endif
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item px-0">
                        <a class="btn collapsed" data-toggle="collapse" href="#collapseExample2" role="button"
                            aria-expanded="true" aria-controls="collapseExample2">
                            会員割引<span class="mr-3"><i class="fa fa-caret-down" aria-hidden="true"></i></span>
                        </a>
                        <div class="collapse" id="collapseExample2">
                            <div class="card card-body mt-2">
                                <div class="radio">
                                    <label for="cradioYes{{ $orders->id }}">
                                        <span>会員</span>
                                    </label>
                                    @if (!empty($orderCouponMember))
                                        <input type="radio" id="cradioYes{{ $orders->id }}" class="radio style-2"
                                            name="coupon_member_{{ $orders->id }}"
                                            value="{{ $orderCouponMember->pivot->coupon_id }}"
                                            attr-name="{{ $orderCouponMember->pivot->name }}"
                                            attr-share-ids="{{ $orderCouponMember->shareCouponIds($orders->course_id, $orders->cast_id) }}"
                                            attr-price="{{ $orderCouponMember->pivot->price }}" checked />
                                    @elseif(!empty($couponMember) && count($couponMember))
                                        <input type="radio" id="cradioYes{{ $orders->id }}" class="radio style-2"
                                            name="coupon_member_{{ $orders->id }}"
                                            value="{{ $couponMember[0]->id }}"
                                            attr-name="{{ $couponMember[0]->name }}"
                                            attr-share-ids="{{ $couponMember[0]->share_coupon_ids }}"
                                            attr-price="{{ $couponMember[0]->price }}"
                                            {{ $customer ? '' : 'disabled' }} />
                                    @else
                                        <input type="radio" id="cradioYes{{ $orders->id }}" class="radio style-2"
                                            name="coupon_member_{{ $orders->id }}" value="0" attr-name=""
                                            attr-share-ids=""
                                            attr-price="0" disabled attr-name="" />
                                    @endif
                                </div>
                                <div class=" radio">
                                    <label>
                                        <span>会員では無い</span></label>
                                    <input type="radio" class="radio style-2" name="coupon_member_{{ $orders->id }}"
                                        attr-share-ids=""
                                        attr-name="" value="0" {{ empty($orderCouponMember) ? 'checked' : '' }} />
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item px-0">
                        <a class="btn collapsed" data-toggle="collapse" href="#collapseExample3" role="button"
                            aria-expanded="true" aria-controls="collapseExample3">
                            割引券<span class="mr-3">
                                @if (isset($couponTickets) && count($couponTickets))
                                    <i class="fa fa-caret-down" aria-hidden="true"></i>
                                @endif
                            </span>
                        </a>
                        <div class="collapse" id="collapseExample3">
                            <div class="card card-body mt-2">
                                @foreach ($couponTickets as $item)
                                    <div class="radio">
                                        <label for="cradio{{ $item->id }}{{ $orders->id }}">
                                            <span>{{ $item->name }}</span>
                                        </label>
                                        @if (!empty($orderCouponTicker) && $orderCouponTicker->pivot->coupon_id == $item->id)
                                            <input type="radio" id="cradio{{ $item->id }}{{ $orders->id }}"
                                                class="radio style-2"
                                                value="{{ $orderCouponTicker->pivot->coupon_id }}"
                                                name="coupon_ticket_{{ $orders->id }}"
                                                attr-name="{{ $orderCouponTicker->pivot->name }}"
                                                attr-share-ids="{{ $orderCouponTicker->shareCouponIds($orders->course_id, $orders->cast_id) }}"
                                                attr-price="{{ $orderCouponTicker->pivot->price }}" checked />
                                        @else
                                            <input type="radio" id="cradio{{ $item->id }}{{ $orders->id }}"
                                                class="radio style-2" value="{{ $item->id }}"
                                                name="coupon_ticket_{{ $orders->id }}"
                                                attr-share-ids="{{ $item->share_coupon_ids }}"
                                                attr-name="{{ $item->name }}" attr-price="{{ $item->price }}" />
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-dismiss="modal">確定</button>
            </div>
        </div>
    </div>
</div>
