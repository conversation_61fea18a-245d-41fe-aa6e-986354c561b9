@extends('layouts.default')

@inject('mCast', 'App\Models\Cast' )

@section('appCss')
    <link rel="stylesheet" href="{{ assetMix('css/admin.manage.cast.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ asset(mix('css/admin.styles.css')) }}" type="text/css">
@endsection

@section('title', 'キャスト一覧')

@section('pageName', 'キャスト一覧')

@section('additional-class', 'quick-view')

@section('appContent')
    <div class="manage-cast container" id="list-manage-cast">
        <div class="row">

            <div class="row list-cast">
                @if (!count($listcast))
                    <div class="note">※該当のキャストが御座いません。</div>
                @else
                    @foreach ($listcast as $cast)
                        @if ($cast->status == $mCast::DISPLAY && ($cast->date_end != null && $cast->date_end > now() || $cast->date_end == null))
                            <div class="cast-item">
                                <div class="hashtag">
                                    @if ($cast->new)
                                        <div class="hashtag-1">新人</div>
                                    @endif
                                    @if (!$cast->matto_play)
                                        <div class="hashtag-2">マット無し</div>
                                    @endif
                                    @if ($cast->is_foreigner)
                                        <div class="hashtag-3">外国人 OK</div>
                                    @endif
                                </div>
                                <div class="cast-item-1 ">
                                    <?php $image = $cast->images()->first(); ?>
                                    <div class="image-cast">
                                        <img src="{{ $image->path ? asset($image->path) : '' }}"
                                            class="img-cast">
                                    </div>
                                    <div class="information-cast">
                                        <p> {{ $cast->name_cast }} ({{ $cast->age }}歳)</p>
                                        <p> T : {{ $cast->height }} cm</p>
                                        <div class="body-measurements">
                                            <p>B : {{ $cast->chest }}cm ({{ $cast->cup }} cup)</p>
                                            <p>W : {{ $cast->waist }}cm </p>
                                            <p>H : {{ $cast->hips }}cm</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach
                @endif
            </div>

        </div>
    </div>
@endsection
