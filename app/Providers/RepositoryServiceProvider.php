<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

/**
 * Class RepositoryServiceProvider
 * @package App\Providers
 */
class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(
            \App\Repositories\User\UserRepositoryInterface::class,
            \App\Repositories\User\UserRepository::class,
            \App\Repositories\OptionInformation\OptionInformationRepositoryInterface::class,
            \App\Repositories\OptionInformation\OptionInformationRepository::class,
            \App\Repositories\Option\OptionRepositoryInterface::class,
            \App\Repositories\Option\OptionRepository::class,
            \App\Repositories\Coupon\CouponRepositoryInterface::class,
            \App\Repositories\Coupon\CouponRepository::class,
            \App\Repositories\CastCalendar\CastCalendarRepositoryInterface::class,
            \App\Repositories\CastCalendar\CastCalendarRepository::class,
            \App\Repositories\Order\OrderRepositoryInterface::class,
            \App\Repositories\Order\OrderRepository::class,
            \App\Repositories\PointHistory\PointHistoryRepositoryInterface::class,
            \App\Repositories\PointHistory\PointHistoryRepository::class,
            \App\Repositories\DailyRevenue\DailyRevenueRepositoryInterface::class,
            \App\Repositories\DailyRevenue\DailyRevenueRepository::class
        );
    }
}
