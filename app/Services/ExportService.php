<?php

namespace App\Services;

use App\Exports\MultipleSheetsExport;
use App\Models\Coupon;
use App\Models\Order;
use App\Repositories\Order\OrderRepository;
use App\Repositories\CastCalendar\CastCalendarRepository;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class ExportService
{
    private $orderRepository;
    private $castCalendarRepository;

    public function __construct(
        OrderRepository $orderRepository,
        CastCalendarRepository $castCalendarRepository
    ) {
        $this->orderRepository = $orderRepository;
        $this->castCalendarRepository = $castCalendarRepository;
    }

    /**
     *
     * @param  Carbon $date
     * @return array
     */
    public function getDataExportConfirmPhone($date)
    {
        $dataExport = [];
        $dataColumn = [
            'stt' => '順番',
            'date' => '日付',
            'order_number' => 'お客様番号',
            'phone_number' => '状態',
            'name_cast' => 'キャスト名',
            'name_customer' => 'お客様名',
            'phone' => '電話番号',
            'course' => 'コース名',
            'is_minute_add' => 'スタートプラス',
            'option' => 'オプション',
            'coupon' => '割引',
            'date_start' => '入り時間',
            'date_end' => '上がり時間',
        ];

        $orders = $this->orderRepository->getListOrdersByDate($date);
        foreach ($orders as $key => $item) {
            $data = [];
            $data = [
                'stt' => ++$key,
                'date' => formatDate($item->date_start),
                'order_number' => $item->number,
                'phone_number' => $item->confirm_phone ? '電話確認済み' : '電話確認末',
                'name_cast' => isset($item->cast) ? $item->cast->name_cast : '',
                'name_customer' => $item->name . '  様',
                'phone' => formatPhone($item->phone),
                'course' => isset($item->courses) ? $item->courses->name : '',
                'is_minute_add' =>  $item->is_minute_add ? "有り" : '無し',
            ];

            $strOption = '';
            if (isset($item->options) && count($item->options)) {
                foreach ($item->options as $option) {
                    if ($option->parent) {
                        $strOption .= '(' . $option->pivot->name . ")";
                    } else {
                        $strOption .= $option->pivot->name;
                    }
                }
            } else {
                $strOption = '無し';
            }
            $data['option'] = $strOption;

            $strCoupon = '';
            if (isset($item->coupons) && count($item->coupons)) {
                foreach ($item->coupons as $coupon) {
                    $strCoupon .= $coupon->pivot->name . ", ";
                }
                $strCoupon = trim($strCoupon, ", ");
            } else {
                $strCoupon = '無し';
            }
            $data['coupon'] = $strCoupon;
            $data['date_start'] = formatTime($item->date_start);
            $data['date_end'] = formatTime($item->date_end);
            $dataExport[] = $data;
        }
        return [
            "sheetName" => "ユーザー確認電話",
            "data" => $dataExport,
            "columnName" => $dataColumn,
        ];
    }

    public function getDataExportBooking($date)
    {
        $dataExport = [];
        $listOrder = $this->orderRepository->getOrderInDay($date);
        foreach ($listOrder as $key => $order) {
            $data = [];
            $data['stt'] = $key + 1;
            $data['date'] = formatDate($date);
            $data['number'] = $order->number;
            if ($order->status == Order::STATUS_NO_HAPPEN_YET) {
                $data['text_status'] = '発進前';
            } elseif ($order->status == Order::STATUS_HAPPENING || $order->status == Order::STATUS_EXTENSION) {
                $data['text_status'] = '通常';
            } else {
                $data['text_status'] = '完了';
            }
            $data['name_cast'] = $order->cast()->get()[0]->name_cast;
            $data['name_cus'] = $order->name . ' 様';
            $data['phone'] = formatPhone($order->phone);
            $data['name_course'] = count($order->courses()->get()) ? $order->courses()->get()[0]->name : 'コースが削除されました。';
            $data['plus_time'] = $order->is_minute_add ? '有り' : '無し';
            $textOptionParent = count($order->optionParent()->get()) ? $order->optionParent()->get()[0]->name : '無し';
            $textOptionChild = count($order->optionChild()->get()) ? '(' . $order->optionChild()->get()[0]->name . ')' : '';
            $data['text_option'] = $textOptionParent . ' ' . $textOptionChild;
            $arrCoupon = [];
            foreach ($order->coupons()->get() as $coupon) {
                if ($coupon->type == Coupon::TYPE_COUPON) {
                    $arrCoupon[] = $coupon->name;
                }
                if ($coupon->type == Coupon::TYPE_COUPON_MEMBER) {
                    $arrCoupon[] = $coupon->name;
                }
                if ($coupon->type == Coupon::TYPE_COUPON_TICKET) {
                    $arrCoupon[] = $coupon->name;
                }
            }
            $data['text_coupon'] = $arrCoupon ? implode(',', $arrCoupon) : '無し';
            $data['total_price'] = number_format($order->total_price) . ' 円';
            $data['time_start'] = substr($order->date_start, 11, 8);
            $data['time_end'] = substr($order->date_end, 11, 8);
            $data['time_start_actual'] = substr($order->actual_date_start, 11, 8);
            $data['time_end_actual'] = substr($order->actual_date_end, 11, 8);
            $dataExport[] = $data;
        }
        return [
            "sheetName" => "予約登録可能画面",
            "data" => $dataExport,
            "columnName" => [
                'stt' => '順番',
                "date" => "日付",
                "number" => "お客様番号",
                "text_status" => "状態",
                "name_cast" => "キャスト名",
                "name_cus" => "お客様名",
                "phone" => "電話番号",
                "name_course" => "コース名",
                "plus_time" => "スタートプラス",
                "text_option" => "オプション",
                "text_coupon" => "割引",
                "total_price" => "総金額",
                "time_start" => "入り予定時間",
                "time_end" => "上がり予定時間",
                "time_start_actual" => "実際の入り時間",
                "time_end_actual" => "実際の上がり時間"
            ]
        ];
    }

    /**
     *
     * @param  Carbon $date
     * @return array
     */
    public function getDataExportCastCalendar($date)
    {
        $result = [];
        $i = 0;
        $dateNow = Carbon::now();
        $columnName = [
            'stt' => '順番',
            'date' => '日付',
            'cast_name' => 'キャスト名',
            'status_text' => '状態',
            'time_start' => '出勤予定時間',
            'time_end' => '退勤予定時間',
            'actual_time_start' => '出勤時刻',
            'actual_time_end' => '退勤時刻'
        ];

        if ($date == $dateNow->format('Y-m-d')) {
            $castCalendarRegister = $this->castCalendarRepository->getCastCalendarSortStatus(Carbon::parse(formatDate($date) . ' ' . $dateNow->format('H:i:s')));
        } else {
            $castCalendarRegister = $this->castCalendarRepository->getCastCalendarSortStatus(Carbon::parse(formatDate($date) . ' 23:59:59'));
        }

        $castCalendarNotRegister = $this->castCalendarRepository->getCastCalendarAbsentNotRegister($date);
        foreach ($castCalendarRegister as $castCalendar) {
            $result[$castCalendar->cast_id]['stt'] = ++$i;
            $result[$castCalendar->cast_id]['date'] = formatDate($castCalendar->date_start);
            $result[$castCalendar->cast_id]['cast_name'] = $castCalendar->cast_name;
            $result[$castCalendar->cast_id]['status_text'] = $castCalendar->status_text;
            $result[$castCalendar->cast_id]['time_start'] = $castCalendar->date_start ? Carbon::parse($castCalendar->date_start)->format('H:i:s') : '';
            $result[$castCalendar->cast_id]['time_end'] = $castCalendar->date_end ? Carbon::parse($castCalendar->date_end)->format('H:i:s') : '';
            $result[$castCalendar->cast_id]['actual_time_start'] = $castCalendar->actual_date_start ? Carbon::parse($castCalendar->actual_date_start)->format('H:i:s') : '';
            $result[$castCalendar->cast_id]['actual_time_end'] = $castCalendar->actual_date_end ? Carbon::parse($castCalendar->actual_date_end)->format('H:i:s') : '';
        }

        foreach ($castCalendarNotRegister as $cast) {
            $result[$cast->cast_id]['stt'] = ++$i;
            $result[$cast->cast_id]['date'] = formatDate($date);
            $result[$cast->cast_id]['cast_name'] = $cast->cast_name;
            $result[$cast->cast_id]['status_text'] = '休み';
        }

        return [
            "columnName" => $columnName,
            "sheetName" =>  '出勤登録可能画面',
            "data" => $result
        ];
    }

    public function exportCsv($date)
    {
        $dateCarbon = Carbon::parse($date);
        $dataExport = [];
        $dataExportBooking = $this->getDataExportBooking($dateCarbon);
        $dataExportConfirmPhone = $this->getDataExportConfirmPhone($dateCarbon);
        $dataExportCastCalendar = $this->getDataExportCastCalendar($dateCarbon);
        array_push($dataExport, $dataExportBooking, $dataExportConfirmPhone, $dataExportCastCalendar);
        return Excel::download(new MultipleSheetsExport($dataExport), $date . "タイムテーブルCSV出力.xlsx");
    }
}
