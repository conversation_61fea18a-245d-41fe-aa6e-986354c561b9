<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Contracts\Session\Session;
use Illuminate\Support\Facades\Redis;

class OrderRedisService
{
    public $keyOrders = 'booking_cast';
    public $timeOut = 5; //minute

    public function setOrders($sessionId, $data)
    {
        $orders = $this->getAllArrOrders();
        $data['created_at'] = now()->toDateTimeString();
        if (!$orders) {
            if (isset($data['date_start']) && gettype($data['date_start']) != 'string') $data['date_start'] = $data['date_start']->format('Y-m-d H:i');
            if (isset($data['date_end']) && gettype($data['date_start']) != 'string') $data['date_end'] = $data['date_end']->format('Y-m-d H:i');
            $strData = json_encode([$sessionId => $data]);
            Redis::set($this->keyOrders, $strData);
            return;
        }

        $check = false;
        foreach ($orders as $key => $item) {
            if ($key == $sessionId) {
                if (isset($data['date_start'])) {
                    $data['date_start'] = gettype($data['date_start']) != 'string' ? $data['date_start']->format('Y-m-d H:i') : $data['date_start'];
                }
                if (isset($data['date_end'])) {
                    $data['date_end'] = gettype($data['date_end']) != 'string' ? $data['date_end']->format('Y-m-d H:i') : $data['date_end'];
                }
                $check = true;
                $orders[$key] = $data;
                break;
            }
        }
        if (!$check) {
            $orders[$sessionId] = $data;
        }
        Redis::del($this->keyOrders);
        Redis::set($this->keyOrders, json_encode($orders));
        return;
    }

    public function getOrdersById($sessionId)
    {
        $orders = $this->getAllArrOrders();
        if (!$orders) return [];
        foreach ($orders as $key => $item) {
            if ($key == $sessionId) {
                if (isset($item['date_start'])) $item['date_start'] = Carbon::parse($item['date_start']);
                if (isset($item['date_end'])) $item['date_end'] = Carbon::parse($item['date_end']);
                return $item;
            }
        }
        return [];
    }

    public function forgetOrdersById($sessionId)
    {
        $orders = $this->getAllArrOrders();
        if (!$orders) return;
        foreach ($orders as $key => $item) {
            if ($key == $sessionId && isset($orders[$key])) {
                unset($orders[$key]);
            }
        }
        Redis::del($this->keyOrders);
        Redis::set($this->keyOrders, json_encode($orders));
        return;
    }

    public function unsetKey($sessionId, $keyDelete)
    {
        $orders = $this->getAllArrOrders();
        if (!$orders) return;
        foreach ($orders as $key => $item) {
            if ($key == $sessionId && isset($orders[$key]) && isset($orders[$key][$keyDelete])) {
                unset($orders[$key][$keyDelete]);
            }
        }
        Redis::del($this->keyOrders);
        Redis::set($this->keyOrders, json_encode($orders));
        return;
    }

    public function getAllOrders()
    {
        $orders = Redis::get($this->keyOrders);
        if (!$orders) {
            return [];
        }
        return json_decode($orders);
    }

    public function getAllArrOrders()
    {
        $orders = Redis::get($this->keyOrders);
        if (!$orders) {
            return [];
        }
        return json_decode($orders, true);
    }

    public function isTimeDuplicate($castId, $dateChoose, $dateChooseEnd, $breakTime, $sessionId = null)
    {
        $dateStart = clone $dateChoose;
        $orders = $this->getAllArrOrders();
        foreach ($orders as $key => $item) {
            if ($castId == $item['cast_id'] && isset($item['date_start']) && $key != $sessionId) {
                $item['date_start'] = Carbon::parse($item['date_start']);
                $item['date_end'] = Carbon::parse($item['date_end']);
                if (
                    $item['date_start'] < $dateChooseEnd->addMinutes($breakTime) &&
                    $item['date_end']->addMinutes($breakTime) > $dateStart->second(59)
                ) {
                    return true;
                }
            }
        }
        return false;
    }

    public function removeBookingTimeOut()
    {
        $now = Carbon::now();
        $orders = $this->getAllArrOrders();
        if (!$orders) return;
        foreach ($orders as $key => $item) {
            $createdAt = Carbon::parse($item['created_at']);
            if ($now->diffInMinutes($createdAt) >= $this->timeOut) {
                unset($orders[$key]);
            }
        }
        Redis::del($this->keyOrders);
        Redis::set($this->keyOrders, json_encode($orders));
        return;
    }
}
