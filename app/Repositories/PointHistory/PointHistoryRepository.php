<?php

namespace App\Repositories\PointHistory;

use App\Models\PointHistory;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\DB;

class PointHistoryRepository extends BaseRepository implements PointHistoryRepositoryInterface
{
    public $typeAccumulatePoint = PointHistory::TYPE_ACCUMULATE_POINT;

    public function getModel()
    {
        return PointHistory::class;
    }

    public function insertPointHistory($customerId, $numberPoint, $type, $note = null)
    {
        if (!$numberPoint) return;
        $dataInsert = [
            'customer_id' => $customerId,
            'point' => $numberPoint,
            'type' => $type,
            'note' => $note,
        ];
        $this->model->create($dataInsert);
        return;
    }
}
