<?php

namespace App\Repositories\Coupon;

use App\Repositories\RepositoryInterface;

interface CouponRepositoryInterface extends RepositoryInterface
{
   function getModel();

   /**
    * get list coupon
    *
    * @param  array $filters
    * @return \Illuminate\Http\Response
    */
   function getListCoupon($filters = []);

   /**
    *
    * @param  int $id
    * @param  string $type
    * @return  boolean
    */
   public function updateOrdinalNumber($id, $type);

   /**
    * couponMember
    *
    * @return object
    */
   public function couponMember();

   /**
    * getOrdinalNumberMax
    *
    * @return int
    */
   public function getOrdinalNumberMax();

   /**
    * delete coupon by id
    *
    * @param int $id
    * @return boolean
    */
   public function destroy($id);

   /**
    * get list price by id
    *
    * @param  int $id
    * @return array
    */
   public function getListPrice($id);


   /**
    * @param  Carbon $number
    * @return boolean
    */
   public function isDayInWeek($date);

   /**
    * @param  Carbon $number
    * @return boolean
    */
   public function isWeekendHoliday($date);

   /**
    * get list coupon by courses and cast id, couponIds
    *
    * @param  int $courseId
    * @param  int $castId
    * @param  int|null $typeCoupon
    * @param  array|null $couponIds
    * @return collection|null
    */
   public function getCouponByCoursesCast($courseId, $castId, $typeCoupon = null, $couponIds = []);

   /**
    * get coupon applicable
    *
    * @param  collection $coupons
    * @param  mixed|carbon $dateStart
    * @return collection|null
    */
   public function getCouponApplicable($coupons, $dateStart);


   /**
    * get list coupon by courses and cast id, couponIds
    * and check date applicable coupon
    *
    * @param  int $courseId
    * @param  int $castId
    * @param  mixed|carbon $dateStart
    * @param  int|null $typeCoupon
    * @return collection|null
    */
   public function getListCouponByCoursesCast($courseId, $castId,  $dateStart, $typeCoupon = null);


   /**
    * @param  int $courseId
    * @param  int $castId
    * @param  mixed|carbon $dateStart
    * @param  array $couponIds
    * @return collection|null
    */
   public function getCouponMergeCouponId($courseId, $castId, $dateStart, $couponIds);
}
