<?php

namespace App\Repositories\DailyRevenue;

use App\Models\DailyRevenue;
use App\Repositories\BaseRepository;
use Carbon\Carbon;

class DailyRevenueRepository extends BaseRepository implements DailyRevenueRepositoryInterface
{
    public function getModel()
    {
        return DailyRevenue::class;
    }

    public function findByDate($date)
    {
        if ($date instanceof Carbon) {
            $date = $date->toDateString();
        }
        return $this->model->whereDate('date', $date)->first();
    }

    public function getFirst15DaysStatistics($date)
    {
        if (!($date instanceof Carbon)) {
            $date = Carbon::parse($date);
        }

        $startOfMonth = $date->copy()->startOfMonth();
        $day15 = $date->copy()->startOfMonth()->addDays(14); // Day 1-15

        return $this->model
            ->selectRaw('SUM(card_orders_count) as total_orders')
            ->selectRaw('SUM(card_orders_total) as total_price')
            ->selectRaw('SUM(card_orders_tax) as total_tax')
            ->selectRaw('SUM(card_orders_for_cast) as total_price_cast')
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereDay('date', '<=', 15)
            ->first();
    }
}
