<?php

namespace App\Repositories\DailyRevenue;

use App\Repositories\RepositoryInterface;
use Carbon\Carbon;

interface DailyRevenueRepositoryInterface extends RepositoryInterface
{
    /**
     * Get the model instance
     *
     * @return \App\Models\DailyRevenue
     */
    public function getModel();

    /**
     * Find revenue by date
     *
     * @param string|Carbon $date
     * @return \App\Models\DailyRevenue|null
     */
    public function findByDate($date);
}
