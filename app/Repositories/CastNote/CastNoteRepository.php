<?php

namespace App\Repositories\CastNote;

use App\Models\CastNote;
use App\Repositories\BaseRepository;

class CastNoteRepository extends BaseRepository implements CastNoteRepositoryInterface
{
    protected $perPage = 50;

    public function getModel()
    {
        return CastNote::class;
    }

    public function updateOrCreate($data)
    {
        $note = $this->model::where('date', $data['date'])
            ->where('cast_id', $data['cast_id'])
            ->first();
        if ($note) {
            $note->content = $data['content'];
            $note->updated_by = $data['updated_by'];
            return $note->save();
        }
        return $this->model::insert($data);
    }
}
