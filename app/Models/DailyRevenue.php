<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DailyRevenue extends Model
{
    use SoftDeletes;

    protected $table = 'daily_revenues';

    protected $fillable = [
        'date',
        'total_orders',
        'total_customers',
        'total_price_cast',
        'cash_orders_count',
        'cash_orders_total',
        'cash_orders_total_support_for_cast',
        'cash_orders_total_unsupported_for_cast',
        'card_orders_count',
        'card_orders_total',
        'card_orders_tax',
        'card_orders_for_cast',
        'total_cast',
        'total_cast_working',
        'total_price_for_cast',
        'dormitory_price',
        'living_expenses',
        'decorate_room_expenses',
        'shop_costs',
        'other_expenses',
        'extension_orders_count',
        'total_extension_minutes',
    ];

    protected $dates = [
        'date',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $casts = [
        'total_orders' => 'integer',
        'total_customers' => 'integer',
        'total_price_cast' => 'decimal:2',
        'cash_orders_count' => 'integer',
        'cash_orders_total' => 'decimal:2',
        'cash_orders_total_support_for_cast' => 'decimal:2',
        'cash_orders_total_unsupported_for_cast' => 'decimal:2',
        'card_orders_count' => 'integer',
        'card_orders_total' => 'decimal:2',
        'card_orders_tax' => 'decimal:2',
        'card_orders_for_cast' => 'decimal:2',
        'total_cast' => 'integer',
        'total_cast_working' => 'integer',
        'total_price_for_cast' => 'decimal:2',
        'dormitory_price' => 'decimal:2',
        'living_expenses' => 'decimal:2',
        'decorate_room_expenses' => 'decimal:2',
        'shop_costs' => 'decimal:2',
        'other_expenses' => 'decimal:2',
        'extension_orders_count' => 'integer',
        'total_extension_minutes' => 'decimal:2',
    ];
}
