<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use App\Models\Course;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Coupon extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'quantity',
        'unlimited',
        'status',
        'applicable_date_type',
        'date_start',
        'date_end',
        'time_start',
        'time_end',
        'ordinal_number',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];
    protected $appends = ['type_text'];

    const STATUS_DRAFT = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 2;

    const TYPE_COUPON = 1;
    const TYPE_COUPON_MEMBER = 2;
    const TYPE_COUPON_TICKET = 3;

    const TYPE_APPLICABLE_DAY_WEEK = 1;
    const TYPE_APPLICABLE_NOT_HOLIDAY_WEEKEND = 2;
    const TYPE_APPLICABLE_HOLIDAY_WEEKEND = 3;

    const ID_COUPON_30 = 1;

    const LIMITED = 0;
    const UNLIMITED = 1;

    /**
     * The courses that belong to the coupon.
     */
    public function courses()
    {
        return $this->belongsToMany(Course::class)->withPivot('price');
    }

    public function getStatusAttribute($value)
    {
        $arrLabel = $this->getLabelStatus();
        if (array_key_exists($value, $arrLabel)) {
            return $arrLabel[$value];
        }
        return;
    }

    public function getTypeTextAttribute()
    {
        $arrLabel = $this->getLabelTypes();
        if (array_key_exists($this->type, $arrLabel)) {
            return $arrLabel[$this->type];
        }
        return;
    }

    public function getLabelStatus()
    {
        return [
            static::STATUS_ACTIVE => '公開',
            static::STATUS_INACTIVE => '非公開'
        ];
    }

    public function getLabelTypes()
    {
        return [
            static::TYPE_COUPON => '割引',
            static::TYPE_COUPON_MEMBER => '会員割引',
            static::TYPE_COUPON_TICKET => '割引券',
        ];
    }

    public function shareCoupons()
    {
        return $this->hasMany(ShareCoupon::class, 'coupon_id', 'id');
    }

    public function shareCouponIds($courseId, $castId)
    {
        $share_coupons = DB::table('share_coupons')
            ->select(
                "share_coupons.coupon_id as coupon_id",
                DB::raw("group_concat(share_coupons.share_coupon_id SEPARATOR ', ') as share_coupon_ids")
            )
            ->join('coupon_course', 'coupon_course.coupon_id', 'share_coupons.share_coupon_id')
            ->join('cast_coupon', "cast_coupon.coupon_id", '=', "share_coupons.share_coupon_id")
            ->where("coupon_course.course_id", $courseId)
            ->where("cast_coupon.cast_id", $castId)
            ->where("share_coupons.coupon_id", $this->id)
            ->groupBy("share_coupons.coupon_id")
            ->first();
        return $share_coupons ? $share_coupons->share_coupon_ids : '';
    }
}
