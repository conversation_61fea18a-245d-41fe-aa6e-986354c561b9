<?php

namespace App\Http\Controllers\Admin;


use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Http\Controllers\Controller;
use App\Services\UploadService;

class UploadController extends Controller
{

    public function thumb(Request $request)
    {
        return redirect(resizeImage($request->get("name"), 200));
    }
    public function index(Request $request)
    {
        request()->validate([
            'file'  => 'required|max:20480',
        ]);
        if ($files = $request->file('file')) {
            $filePath  = UploadService::upload($files);
            // rename($oldFile, $newFile);
            return Response()->json([
                "success" => true,
                "url" => url($filePath),
                "fileName" => basename($filePath),
                "fileUrl" => url($filePath)
            ]);
        }

        return Response()->json([
            "success" => false,
            "file" => ''
        ]);
    }
}
