<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProfileUserRequest;
use App\Models\UserRole;
use App\Repositories\User\UserRepository;
use App\Services\PermissionPage;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{

    private $repository;
    private $permission;

    public function __construct(UserRepository $repository, PermissionPage $permission)
    {
        $this->repository = $repository;
        $this->permission = $permission;
    }

    public function index(Request $request)
    {
        if (Auth::id() != 1) {
            return redirect()->route('admin.index');
        }
        $this->data["users"] =  $this->repository->getAll();
        return $this->renderView("admin.user.index");
    }

    public function create(Request $request)
    {
        if (Auth::id() != 1) {
            return redirect()->route('admin.index');
        }

        $this->data["groupPageRoute"] = $this->permission->getGroupPageRoute();

        return $this->renderView("admin.user.create");
    }

    public function store(ProfileUserRequest $request)
    {
        $data = [
            'name' => $request->name,
            'email' => $request->account,
        ];
        $data['password'] = Hash::make($request->password_new);
        $arrPermission = $this->getPermissionRequest($request->all());

        DB::beginTransaction();
        try {
            $user = $this->repository->create($data);
            $this->permission->createPermission($user->id, $arrPermission);
            DB::commit();
        } catch (\Exception $ex) {
            DB::rollBack();
            Log::info($ex);
        }
        return redirect(route("admin.users.index"));
    }

    /**
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $this->data['user'] = $this->repository->find($id);
        if (!$this->data['user']) {
            return abort(404);
        }
        $this->data["groupPageRoute"] = $this->permission->getGroupPageRoute();
        $this->data["dataPermission"] = $this->permission->getPermissionById($this->data['user']->id);
        return $this->renderView('admin.user.edit');
    }

    public function update(ProfileUserRequest $request)
    {
        $user = Auth::user();
        if ($user->id != 1) {
            return redirect()->route('admin.index');
        }
        $data = [
            'name' => $request->name,
        ];

        if ($request->password_old) {
            if (!Hash::check($request->password_old, $user->password)) {
                return back()->withInput()->with('error_password', '※現在のパスワードが違います。');
            } else {
                $data['password'] = Hash::make($request->password_new);
            }
        } elseif (isset($this->password_new)) {
            $data['password'] = Hash::make($request->password_new);
        } else {
        }

        $arrPermission = $this->getPermissionRequest($request->all());

        DB::beginTransaction();
        try {
            $this->repository->update($request->id, $data);
            if ($arrPermission) {
                $this->permission->updatePermissionById($request->id, $arrPermission);
            }
            DB::commit();
        } catch (\Exception $ex) {
            DB::rollBack();
            Log::info($ex);
        }

        return redirect(route("admin.users.index"));
    }

    public function destroy($id)
    {
        User::where("id", $id)->delete();
    }

    public function updateStatus($id)
    {
        $user = $this->repository->find($id);
        if (!$user) {
            return Response()->json([
                "success" => 0,
                'message' => '※対象のデータはありません。'
            ]);
        }
        if ($user->id == 1) {
            return Response()->json([
                "success" => 1,
                'textStatus' => $user->status_text,
            ]);
        }
        if ($user->status == User::STATUS_INACTIVE) {
            $user->status = User::STATUS_ACTIVE;
        } else {
            $user->status = User::STATUS_INACTIVE;
        }
        $user->update();
        return Response()->json([
            "success" => 1,
            'textStatus' => $user->status_text,
            'status' => $user->status
        ]);
    }

    public function editProfile()
    {
        $user = Auth::user();
        return view('admin.user.edit_profile', compact('user'));
    }

    public function updateProfile(ProfileUserRequest $request)
    {
        $user = Auth::user();
        $data = [
            'name' => $request->name,
        ];
        if ($request->password_old) {
            $data['password'] = Hash::make($request->password_new);
        }
        $this->repository->update($user->id, $data);

        return redirect(route('admin.home.index'));
    }

    public function getPermissionRequest($arrRequest)
    {
        $result = [];

        if (!$arrRequest) {
            return null;
        }
        foreach ($arrRequest as $key => $value) {
            if (strpos($key, "permission_") === false) continue;
            $result[str_replace('permission_', '', $key)] = $value;
        }
        return $result;
    }
}
