<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => 'required',
            'phone' => 'required',
        ];

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required' => '※名前を入力してください。',
            'phone.required' => '※電話番号を入力してください。',
        ];
    }
}
