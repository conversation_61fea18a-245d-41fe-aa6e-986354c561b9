<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CastCalendarRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'price_cast' => "max:8",
        ];

        return $rules;
    }

    public function messages()
    {
        return [
            'price_cast.max' => " ※０～８数字以内で入力してください",
        ];
    }
}
