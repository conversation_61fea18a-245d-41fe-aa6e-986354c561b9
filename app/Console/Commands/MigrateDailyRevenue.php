<?php

namespace App\Console\Commands;

use App\Models\DailyRevenue;
use App\Models\Order;
use App\Models\CastCalendar;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CostShop\CostShopRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class MigrateDailyRevenue extends Command
{
    protected $signature = 'migrate:daily-revenue';
    protected $description = 'Migrate data to daily_revenue table for testing';

    private $orderRepository;
    private $castCalendarRepository;
    private $costShopRepository;
    private $castPriceLivingRepository;
    private $priceDecorateRoomRepository;

    public function __construct(
        OrderRepository $orderRepository,
        CastCalendarRepository $castCalendarRepository,
        CostShopRepository $costShopRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository
    ) {
        parent::__construct();
        $this->orderRepository = $orderRepository;
        $this->castCalendarRepository = $castCalendarRepository;
        $this->costShopRepository = $costShopRepository;
        $this->castPriceLivingRepository = $castPriceLivingRepository;
        $this->priceDecorateRoomRepository = $priceDecorateRoomRepository;
    }

    public function handle()
    {
        $date = Carbon::parse('2025-05-25');
        $this->info("Migrating data for {$date->format('Y-m-d')}...");

        // Get data from repositories
        $totalCustomers = $this->orderRepository->getTotalCustomerPresent($date, true, true) ?: 0;
        $totalPriceCastData = $this->castCalendarRepository->getTotalPriceCastPresentByTime($date, $date, true);
        $totalPriceCast = isset($totalPriceCastData->total) ? $totalPriceCastData->total : 0;

        $arrStatus = [
            Order::STATUS_NO_HAPPEN_YET,
            Order::STATUS_HAPPENING,
            Order::STATUS_EXTENSION,
            Order::STATUS_FINISH
        ];

        $filtersWork['arr_status'] = [
            CastCalendar::STATUS_PREPARE,
            CastCalendar::STATUS_WORKING
        ];

        $filtersWorking['arr_status'] = [
            CastCalendar::STATUS_WORKING
        ];

        $filtersCast['arr_status'] = [
            CastCalendar::STATUS_WORKING,
            CastCalendar::STATUS_FINISH,
        ];

        $filtersCastCalendar['arr_status'] = [
            array_unique(array_merge($filtersCast['arr_status'], $filtersWork['arr_status'], $filtersWorking['arr_status'])),
        ];

        $totalCastCalendars = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersCastCalendar, true);
        $totalCast = $totalCastCalendars->whereIn('status', $filtersCast['arr_status'])->count() ?: 0;
        $totalCastWorking = $totalCastCalendars->whereIn('status', $filtersWorking['arr_status'])->count() ?: 0;

        $totalPriceCastPaied = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($date, $date);
        $totalPriceExpenses = $this->orderRepository->getTotalPriceExpensesOrdersPaid($date, $date);
        $priceLiving = $this->castPriceLivingRepository->totalPriceByTime($date->toDateString(), $date->toDateString()) ?: 0;
        $priceDecorateRoom = $this->priceDecorateRoomRepository->getTotalPriceByTime($date, $date) ?: 0;

        $totalPriceOrderByCard = $this->orderRepository->totalOrdersByCardByTime($date->toDateString(), $date->toDateString());
        $totalPriceOrderByCash = $this->orderRepository->totalOrdersByCashByTime($date->toDateString(), $date->toDateString());
        $costShop = $this->costShopRepository->totalPriceByTime($date->toDateString(), $date->toDateString()) ?: 0;

        $extensionData = $this->orderRepository->getTotalOrderExtension($date, true);
        $extensionCount = $extensionData ? $extensionData->count_extension : 0;
        $extensionMinutes = $extensionData ? $extensionData->total_minute_extension : 0;

        // Create or update daily revenue record
        DailyRevenue::updateOrCreate(
            ['date' => $date->toDateString()],
            [
                'total_customers' => $totalCustomers,
                'total_price_cast' => $totalPriceCast,
                'total_cast' => $totalCast,
                'total_cast_working' => $totalCastWorking,
                'extension_orders_count' => $extensionCount,
                'total_extension_minutes' => $extensionMinutes,
                'total_price_for_cast' => isset($totalPriceCastPaied->price_for_cast) ? $totalPriceCastPaied->price_for_cast : 0,
                'dormitory_price' => isset($totalPriceCastPaied->total_dormitory_price) ? $totalPriceCastPaied->total_dormitory_price : 0,
                'other_expenses' => isset($totalPriceExpenses[0]->total_price_expenses) ? $totalPriceExpenses[0]->total_price_expenses : 0,
                'living_expenses' => $priceLiving,
                'decorate_room_expenses' => $priceDecorateRoom,
                'card_orders_count' => isset($totalPriceOrderByCard['total']) ? $totalPriceOrderByCard['total'] : 0,
                'card_orders_total' => isset($totalPriceOrderByCard['total_price']) ? $totalPriceOrderByCard['total_price'] : 0,
                'card_orders_tax' => isset($totalPriceOrderByCard['total_tax_price']) ? $totalPriceOrderByCard['total_tax_price'] : 0,
                'card_orders_for_cast' => isset($totalPriceOrderByCard['total_price_cast']) ? $totalPriceOrderByCard['total_price_cast'] : 0,
                'cash_orders_total' => isset($totalPriceOrderByCash['total_price']) ? $totalPriceOrderByCash['total_price'] : 0,
                'cash_orders_total_unsupported_for_cast' => isset($totalPriceOrderByCash['total_price_cast_not_support']) ? $totalPriceOrderByCash['total_price_cast_not_support'] : 0,
                'cash_orders_total_support_for_cast' => isset($totalPriceOrderByCash['total_price_support']) ? $totalPriceOrderByCash['total_price_support'] : 0,
                'shop_costs' => $costShop
            ]
        );

        $this->info('Migration completed successfully!');
    }
}
