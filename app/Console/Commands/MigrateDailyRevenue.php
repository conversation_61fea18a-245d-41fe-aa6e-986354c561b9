<?php

namespace App\Console\Commands;

use App\Models\DailyRevenue;
use App\Models\Order;
use App\Models\CastCalendar;
use App\Constants\Orders;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CostShop\CostShopRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class MigrateDailyRevenue extends Command
{
    protected $signature = 'migrate:daily-revenue {--month=} {--year=}';
    protected $description = 'Migrate data to daily_revenue table for specified month/year or May 2025';

    private $orderRepository;
    private $castCalendarRepository;
    private $costShopRepository;
    private $castPriceLivingRepository;
    private $priceDecorateRoomRepository;

    public function __construct(
        OrderRepository $orderRepository,
        CastCalendarRepository $castCalendarRepository,
        CostShopRepository $costShopRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository
    ) {
        parent::__construct();
        $this->orderRepository = $orderRepository;
        $this->castCalendarRepository = $castCalendarRepository;
        $this->costShopRepository = $costShopRepository;
        $this->castPriceLivingRepository = $castPriceLivingRepository;
        $this->priceDecorateRoomRepository = $priceDecorateRoomRepository;
    }

    public function handle()
    {
        // Get month and year from options or default to May 2025
        $month = $this->option('month') ?: 5;
        $year = $this->option('year') ?: 2025;

        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();

        $this->info("Migrating daily revenue data for {$startDate->format('F Y')}...");
        $this->info("Date range: {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

        $currentDate = $startDate->copy();
        $totalDays = $endDate->day;
        $processedDays = 0;

        while ($currentDate <= $endDate) {
            $processedDays++;
            $this->info("Processing day {$processedDays}/{$totalDays}: {$currentDate->format('Y-m-d')}...");

            $this->migrateDayData($currentDate);

            $currentDate->addDay();
        }

        $this->info("Migration completed successfully! Processed {$processedDays} days.");
    }

    private function migrateDayData($date)
    {
        // Get data from repositories using the same logic as the realtime calculation
        $totalCustomers = $this->orderRepository->getTotalCustomerPresent($date, false, false) ?: 0;
        $totalPriceCastData = $this->castCalendarRepository->getTotalPriceCastPresentByTime($date, $date, false);
        $totalPriceCast = isset($totalPriceCastData->total) ? $totalPriceCastData->total : 0;

        $arrStatus = [
            Order::STATUS_NO_HAPPEN_YET,
            Order::STATUS_HAPPENING,
            Order::STATUS_EXTENSION,
            Order::STATUS_FINISH
        ];

        $filtersWork['arr_status'] = [
            CastCalendar::STATUS_PREPARE,
            CastCalendar::STATUS_WORKING
        ];

        $filtersWorking['arr_status'] = [
            CastCalendar::STATUS_WORKING
        ];

        $filtersCast['arr_status'] = [
            CastCalendar::STATUS_WORKING,
            CastCalendar::STATUS_FINISH,
        ];

        $filtersCastCalendar['arr_status'] = [
            array_unique(array_merge($filtersCast['arr_status'], $filtersWork['arr_status'], $filtersWorking['arr_status'])),
        ];

        $totalCastCalendars = $this->castCalendarRepository->getCastCalendarSortStatus($date, $filtersCastCalendar, false);
        $totalCast = $totalCastCalendars->whereIn('status', $filtersCast['arr_status'])->count() ?: 0;
        $totalCastWorking = $totalCastCalendars->whereIn('status', $filtersWorking['arr_status'])->count() ?: 0;

        $totalPriceCastPaied = $this->castCalendarRepository->getTotalPriceCastPaiedByTime($date, $date, false);
        $totalPriceExpenses = $this->orderRepository->getTotalPriceExpensesOrdersPaid($date, $date, false);
        $priceLiving = $this->castPriceLivingRepository->totalPriceByTime($date->toDateString(), $date->toDateString()) ?: 0;
        $priceDecorateRoom = $this->priceDecorateRoomRepository->getTotalPriceByTime($date, $date) ?: 0;

        $totalPriceOrderByCard = $this->orderRepository->totalOrdersByCardByTime($date->toDateString(), $date->toDateString());
        $totalPriceOrderByCash = $this->orderRepository->totalOrdersByCashByTime($date->toDateString(), $date->toDateString());
        $costShop = $this->costShopRepository->totalPriceByTime($date->toDateString(), $date->toDateString()) ?: 0;

        $extensionData = $this->orderRepository->getTotalOrderExtension($date, false);
        $extensionCount = $extensionData ? ($extensionData->count_extension ?: 0) : 0;
        $extensionMinutes = $extensionData ? ($extensionData->total_minute_extension ?: 0) : 0;

        // Get total orders count
        $totalOrders = $this->orderRepository->getOrderPayOfDay($date->toDateString())->count() ?: 0;

        // Get cash orders count
        $cashOrdersCount = $this->orderRepository->getOrderPayOfDay($date->toDateString())
            ->where('type_pay', Orders::TYPE_PAYMENT_CASH)->count() ?: 0;

        // Create or update daily revenue record
        DailyRevenue::updateOrCreate(
            ['date' => $date->toDateString()],
            [
                'total_orders' => $totalOrders,
                'total_customers' => $totalCustomers,
                'total_price_cast' => $totalPriceCast,
                'cash_orders_count' => $cashOrdersCount,
                'cash_orders_total' => isset($totalPriceOrderByCash['total_price']) ? $totalPriceOrderByCash['total_price'] : 0,
                'cash_orders_total_support_for_cast' => isset($totalPriceOrderByCash['total_price_support']) ? $totalPriceOrderByCash['total_price_support'] : 0,
                'cash_orders_total_unsupported_for_cast' => isset($totalPriceOrderByCash['total_price_cast_not_support']) ? $totalPriceOrderByCash['total_price_cast_not_support'] : 0,
                'card_orders_count' => isset($totalPriceOrderByCard['total']) ? $totalPriceOrderByCard['total'] : 0,
                'card_orders_total' => isset($totalPriceOrderByCard['total_price']) ? $totalPriceOrderByCard['total_price'] : 0,
                'card_orders_tax' => isset($totalPriceOrderByCard['total_tax_price']) ? $totalPriceOrderByCard['total_tax_price'] : 0,
                'card_orders_for_cast' => isset($totalPriceOrderByCard['total_price_cast']) ? $totalPriceOrderByCard['total_price_cast'] : 0,
                'total_cast' => $totalCast,
                'total_cast_working' => $totalCastWorking,
                'total_price_for_cast' => isset($totalPriceCastPaied->price_for_cast) ? $totalPriceCastPaied->price_for_cast : 0,
                'dormitory_price' => isset($totalPriceCastPaied->total_dormitory_price) ? $totalPriceCastPaied->total_dormitory_price : 0,
                'living_expenses' => $priceLiving,
                'decorate_room_expenses' => $priceDecorateRoom,
                'shop_costs' => $costShop,
                'other_expenses' => isset($totalPriceExpenses[0]->total_price_expenses) ? $totalPriceExpenses[0]->total_price_expenses : 0,
                'extension_orders_count' => $extensionCount,
                'total_extension_minutes' => $extensionMinutes,
            ]
        );
    }
}
