version: '3'
services:
  redis:
    image: redis:alpine
    # container_name: myapp-redis
    command: redis-server --appendonly yes --requirepass "${REDIS_PASSWORD}"
    volumes:
      - ./data/redis:/redist-data
      - ./docker/redis/sysctl.conf:/etc/sysctl.conf
    ports:
      - "6379:6379"
    networks:
      - app-network
  adminer:
    image: adminer
    restart: always
    ports:
      - ${ADMINER_PORT}:8080
    links:
      - db
    depends_on: 
      - db
    networks:
      - app-network
  #MySQL Service
  db:
    image: mysql:5.7.22
    # container_name: db
    restart: unless-stopped
    tty: true
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: db
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql/
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - app-network 
    networks:
      - app-network
#Docker Networks
networks:
  app-network:
    driver: bridge
#Volumes
volumes:
  dbdata:
    driver: local
    